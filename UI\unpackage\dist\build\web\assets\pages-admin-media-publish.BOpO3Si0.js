import{_ as t,v as e,K as a,c as i,w as s,i as l,o as r,h as n,j as d,t as o,aC as m,$ as h,ar as c,C as u}from"./index-Bl1z7-qC.js";import{c as f}from"./batch.Bdv1419a.js";import{m as g}from"./media-common.7odKdPQQ.js";import{s as T,b as D}from"./admin-helpers.CPO-gyAZ.js";const p=t({mixins:[g],data:()=>({videoId:0,batchTitle:"",batchDescription:"",startDate:"",startTime:"",endDate:"",endTime:""}),computed:{isFormValid(){return this.batchTitle&&this.batchTitle.trim()&&this.batchTitle.trim().length>=2}},onLoad(t){const{videoId:e,id:a}=t??{};(e||a)&&(this.videoId=T(e??a)??0),this.initDateTimeValues()},methods:{initDateTimeValues(){const t=new Date;this.startDate=this.formatDate(t),this.startTime=this.formatTime(t);const e=new Date;e.setDate(e.getDate()+30),this.endDate=this.formatDate(e),this.endTime=this.formatTime(e)},formatDate:t=>`${t.getFullYear()}-${String(t.getMonth()+1).padStart(2,"0")}-${String(t.getDate()).padStart(2,"0")}`,formatTime:t=>`${String(t.getHours()).padStart(2,"0")}:${String(t.getMinutes()).padStart(2,"0")}`,onStartDateChange(t){this.startDate=t.detail.value},onStartTimeChange(t){this.startTime=t.detail.value},onEndDateChange(t){this.endDate=t.detail.value},onEndTimeChange(t){this.endTime=t.detail.value},getStartDateTime(){if(!this.startDate||!this.startTime)return"";let t=this.startDate;return t.includes(" ")&&(t=t.split(" ")[0]),`${t}T${this.startTime}:00`},getEndDateTime(){if(!this.endDate||!this.endTime)return"";let t=this.endDate;return t.includes(" ")&&(t=t.split(" ")[0]),`${t}T${this.endTime}:00`},async createBatch(){try{const t=this.validateForm();if(!t.valid)return void e({title:t.message,icon:"none"});const a={name:this.batchTitle.trim(),description:this.batchDescription.trim()||"暂无描述",videoId:this.videoId,startTime:this.getStartDateTime(),endTime:this.getEndDateTime(),redPacketAmount:0},i=await D((()=>f(a)),"创建批次中...","创建批次失败");if(!i.success)throw new Error(i.msg||"创建批次失败");e({title:"批次创建成功",icon:"success"}),setTimeout((()=>{this.goBack()}),1500)}catch(t){}},validateForm(){if(!this.batchTitle||""===this.batchTitle.trim())return{valid:!1,message:"请输入批次标题"};if(this.batchTitle.trim().length<2)return{valid:!1,message:"批次标题至少需要2个字符"};if(this.batchTitle.trim().length>50)return{valid:!1,message:"批次标题不能超过50个字符"};if(this.batchDescription&&this.batchDescription.length>200)return{valid:!1,message:"批次描述不能超过200个字符"};if(!this.startDate||!this.startTime)return{valid:!1,message:"请选择活动开始时间"};if(!this.endDate||!this.endTime)return{valid:!1,message:"请选择活动结束时间"};if(!this.videoId||this.videoId<=0)return{valid:!1,message:"视频ID无效，请重新进入页面"};return new Date(this.getStartDateTime())>=new Date(this.getEndDateTime())?{valid:!1,message:"开始时间必须早于结束时间"}:{valid:!0,message:""}},goBack(){a()}}},[["render",function(t,e,a,f,g,T){const D=l,p=h,v=c,b=u;return r(),i(D,{class:"container"},{default:s((()=>[n(D,{class:"publish-form"},{default:s((()=>[n(D,{class:"form-section"},{default:s((()=>[n(D,{class:"section-title"},{default:s((()=>[d("批次基本信息")])),_:1}),n(D,{class:"form-group"},{default:s((()=>[n(D,{class:"form-label required"},{default:s((()=>[d("批次标题")])),_:1}),n(p,{type:"text",class:"form-input",modelValue:g.batchTitle,"onUpdate:modelValue":e[0]||(e[0]=t=>g.batchTitle=t),placeholder:"请输入批次标题（2-50字符）",maxlength:"50"},null,8,["modelValue"]),n(D,{class:"form-hint"},{default:s((()=>[d(o(g.batchTitle.length)+"/50",1)])),_:1})])),_:1}),n(D,{class:"form-group"},{default:s((()=>[n(D,{class:"form-label required"},{default:s((()=>[d("活动开始日期")])),_:1}),n(v,{mode:"date",value:g.startDate,onChange:T.onStartDateChange},{default:s((()=>[n(D,{class:"form-input picker-display"},{default:s((()=>[d(o(g.startDate||"请选择开始日期"),1)])),_:1})])),_:1},8,["value","onChange"])])),_:1}),n(D,{class:"form-group"},{default:s((()=>[n(D,{class:"form-label required"},{default:s((()=>[d("活动开始时间")])),_:1}),n(v,{mode:"time",value:g.startTime,onChange:T.onStartTimeChange},{default:s((()=>[n(D,{class:"form-input picker-display"},{default:s((()=>[d(o(g.startTime||"请选择开始时间"),1)])),_:1})])),_:1},8,["value","onChange"])])),_:1}),n(D,{class:"form-group"},{default:s((()=>[n(D,{class:"form-label required"},{default:s((()=>[d("活动结束日期")])),_:1}),n(v,{mode:"date",value:g.endDate,onChange:T.onEndDateChange},{default:s((()=>[n(D,{class:"form-input picker-display"},{default:s((()=>[d(o(g.endDate||"请选择结束日期"),1)])),_:1})])),_:1},8,["value","onChange"])])),_:1}),n(D,{class:"form-group"},{default:s((()=>[n(D,{class:"form-label required"},{default:s((()=>[d("活动结束时间")])),_:1}),n(v,{mode:"time",value:g.endTime,onChange:T.onEndTimeChange},{default:s((()=>[n(D,{class:"form-input picker-display"},{default:s((()=>[d(o(g.endTime||"请选择结束时间"),1)])),_:1})])),_:1},8,["value","onChange"])])),_:1}),n(D,{class:"action-buttons"},{default:s((()=>[n(b,{class:"action-btn cancel-btn",onClick:T.goBack},{default:s((()=>[m("i",{class:"fa fa-times"}),d(" 取消 ")])),_:1},8,["onClick"]),n(b,{class:"action-btn publish-btn",onClick:e[1]||(e[1]=t=>T.createBatch()),disabled:!T.isFormValid},{default:s((()=>[m("i",{class:"fa fa-check"}),d(" 创建批次 ")])),_:1},8,["disabled"])])),_:1})])),_:1})])),_:1})])),_:1})}],["__scopeId","data-v-51388d47"]]);export{p as default};
