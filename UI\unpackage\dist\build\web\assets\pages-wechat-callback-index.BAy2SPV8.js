import{_ as s,p as e,O as t,S as a,a1 as o,K as r,r as c,b as i,c as n,w as l,a2 as h,i as d,e as u,f as g,g as m,o as f,h as w,j as k,t as y,k as p,l as b}from"./index-Bl1z7-qC.js";import x from"./utils-wechatWebAuth.v7QTqDdJ.js";import{a as C}from"./wechatUserService.DURLCuH_.js";import"./wechat.DoVyc6Fh.js";import"./video-user.DmBugx73.js";const _=s({data:()=>({isLoading:!0,hasError:!1,isSuccess:!1,errorMessage:"",loadingText:"正在处理授权信息...",callbackResult:null,retryCount:0,maxRetries:3}),onLoad(s){console.log("微信授权回调页面加载，参数:",s),this.handleWechatCallback()},methods:{async handleWechatCallback(){try{this.isLoading=!0,this.hasError=!1,this.loadingText="正在处理授权信息...";const s=x.handleCallback();this.callbackResult=s,console.log("微信授权回调结果:",s);const t={success:s.success,code:s.code,state:s.state,stateData:s.stateData,error:s.error,message:s.message,timestamp:(new Date).toLocaleString()};if(console.log("微信授权回调调试信息:",t),!s.success)return setTimeout((()=>{e({url:`/pages/wechat-test/index?debug=${encodeURIComponent(JSON.stringify(t))}`})}),2e3),void this.showError(s.message||"授权失败");if(!s.code)return t.error="no_code",t.message="未获取到授权码",setTimeout((()=>{e({url:`/pages/wechat-test/index?debug=${encodeURIComponent(JSON.stringify(t))}`})}),2e3),void this.showError("未获取到授权码");await this.performLogin(s)}catch(s){console.error("处理微信授权回调失败:",s),this.showError("处理授权信息失败: "+s.message)}},async performLogin(s){try{this.loadingText="正在登录...";const e={code:s.code,state:s.state};console.log("准备调用登录接口，数据:",e);const t=await C(e);if(console.log("登录接口响应:",t),!t.success||!t.data)throw new Error(t.message||"登录失败");await this.handleLoginSuccess(t.data)}catch(e){console.error("登录失败:",e),this.showError("登录失败: "+e.message)}},async handleLoginSuccess(s){try{this.loadingText="登录成功，正在跳转...",s.userInfo&&t("wechatUserInfo",s.userInfo),s.token&&t("wechatUserToken",s.token),this.isLoading=!1,this.isSuccess=!0,this.showToastMessage("登录成功！","success"),setTimeout((()=>{this.redirectAfterLogin()}),1500)}catch(e){console.error("处理登录成功失败:",e),this.showError("保存登录信息失败: "+e.message)}},redirectAfterLogin(){var s,t,o;try{const r={code:null==(s=this.callbackResult)?void 0:s.code,state:null==(t=this.callbackResult)?void 0:t.state,stateData:null==(o=this.callbackResult)?void 0:o.stateData,loginSuccess:!0};console.log("登录成功，跳转回测试页面，调试信息:",r),e({url:`/pages/wechat-test/index?debug=${encodeURIComponent(JSON.stringify(r))}`,fail:s=>{console.error("跳转失败:",s),a({url:"/pages/wechat-test/index"})}})}catch(r){console.error("跳转失败:",r),this.showError("跳转失败: "+r.message)}},showError(s){this.isLoading=!1,this.hasError=!0,this.errorMessage=s,this.showToastMessage(s,"error")},async handleRetry(){this.retryCount>=this.maxRetries?this.showToastMessage("重试次数过多，请稍后再试","warning"):(this.retryCount++,console.log(`第 ${this.retryCount} 次重试`),await this.handleWechatCallback())},goBack(){o().length>1?r():e({url:"/pages/index/index"})},showToastMessage(s,e="success"){this.$refs.uToast.show({message:s,type:e,duration:3e3})}}},[["render",function(s,e,t,a,o,r){const x=c(i("u-loading-icon"),h),C=b,_=d,T=c(i("u-icon"),u),L=c(i("u-button"),g),E=c(i("u-toast"),m);return f(),n(_,{class:"callback-container"},{default:l((()=>[w(_,{class:"callback-content"},{default:l((()=>[o.isLoading?(f(),n(_,{key:0,class:"loading-section"},{default:l((()=>[w(x,{mode:"spinner",size:"40",color:"#186BFF"}),w(C,{class:"loading-text"},{default:l((()=>[k(y(o.loadingText),1)])),_:1})])),_:1})):o.hasError?(f(),n(_,{key:1,class:"error-section"},{default:l((()=>[w(T,{name:"close-circle",size:"60",color:"#F5222D"}),w(C,{class:"error-title"},{default:l((()=>[k("授权失败")])),_:1}),w(C,{class:"error-message"},{default:l((()=>[k(y(o.errorMessage),1)])),_:1}),w(L,{type:"primary",onClick:r.handleRetry,class:"retry-btn"},{default:l((()=>[k(" 重试 ")])),_:1},8,["onClick"]),w(L,{type:"info",onClick:r.goBack,class:"back-btn"},{default:l((()=>[k(" 返回 ")])),_:1},8,["onClick"])])),_:1})):o.isSuccess?(f(),n(_,{key:2,class:"success-section"},{default:l((()=>[w(T,{name:"checkmark-circle",size:"60",color:"#52C41A"}),w(C,{class:"success-title"},{default:l((()=>[k("授权成功")])),_:1}),w(C,{class:"success-message"},{default:l((()=>[k("正在跳转...")])),_:1})])),_:1})):p("",!0)])),_:1}),w(E,{ref:"uToast"},null,512)])),_:1})}],["__scopeId","data-v-bd3cffde"]]);export{_ as default};
