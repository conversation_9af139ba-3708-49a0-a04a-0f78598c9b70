import"./index-Bl1z7-qC.js";import{b as e,p as r,g as t}from"./wechat.DoVyc6Fh.js";let i=null;function o(e){i=e}function s(e,r="info"){console.log(`[WechatWebAuth] [${r.toUpperCase()}] ${e}`),i&&i(`[WechatWebAuth] ${e}`,r)}class n{constructor(){this.config=null,this.initConfig()}initConfig(){s("初始化微信配置..."),"undefined"!=typeof window&&window.APP_CONFIG?(s("从 window.APP_CONFIG 获取微信配置"),this.config=window.APP_CONFIG.wechat,s(`获取到的配置: ${JSON.stringify(this.config)}`)):(s("使用备用微信配置"),this.config={appId:"wx02bacea38f7f3ab5",scope:"snsapi_userinfo",authBaseUrl:"https://open.weixin.qq.com/connect/oauth2/authorize",redirectUri:{development:"http://localhost:5173/pages/wechat-callback/index",production:"https://your-domain.com/pages/wechat-callback/index"}},s(`备用配置: ${JSON.stringify(this.config)}`)),s("微信配置初始化完成")}isH5Environment(){return s("环境检测: H5环境"),!0}isWechatBrowser(){if("undefined"==typeof window)return!1;return window.navigator.userAgent.toLowerCase().includes("micromessenger")}getRedirectUri(){var e;return"undefined"!=typeof window&&(null==(e=window.APP_CONFIG)?void 0:e.debugMode)?this.config.redirectUri.development:this.config.redirectUri.production}generateState(r={}){return e(r)}parseState(e){return r(e)}async buildAuthUrl(e={}){try{const{state:r=this.generateState(),redirectUri:i=this.getRedirectUri(),scope:o=this.config.scope||"snsapi_userinfo"}=e,s={redirectUri:i,state:r,scope:o,inviterId:e.inviterId||null};console.log("调用授权URL接口，参数:",s);const n=await t(s);if(console.log("授权URL接口响应:",n),n.success&&n.data){const e="string"==typeof n.data?n.data:n.data.url||n.data.authUrl;if("string"==typeof e)return e;throw console.error("授权URL格式错误:",n.data),new Error("授权URL格式错误")}throw new Error(n.msg||n.message||"获取授权URL失败")}catch(r){throw console.error("构造授权URL失败:",r),r}}buildAuthUrlDirect(e={}){s(`buildAuthUrlDirect 开始，参数: ${JSON.stringify(e)}`);const{state:r=this.generateState(),redirectUri:t=this.getRedirectUri(),scope:i=this.config.scope||"snsapi_userinfo"}=e,o={appid:this.config.appId,redirect_uri:t,response_type:"code",scope:i,state:r};if(s(`构造URL参数: ${JSON.stringify(o)}`),!this.config.appId)throw new Error("微信AppId未配置");if(!t)throw new Error("回调地址未配置");const n=`https://open.weixin.qq.com/connect/oauth2/authorize?${new URLSearchParams(o).toString()}#wechat_redirect`;return s(`直接构造的授权URL: ${n}`),s(`URL长度: ${n.length}`),n}parseCallbackParams(e=""){let r="";r=e||"undefined"==typeof window?e.includes("?")?e.split("?")[1]:e:window.location.search;const t=new URLSearchParams(r);return{code:t.get("code"),state:t.get("state"),error:t.get("error"),error_description:t.get("error_description")}}async startAuth(e={}){try{s(`startAuth 开始，参数: ${JSON.stringify(e)}`);const i=this.isH5Environment();if(s(`检查H5环境: ${i}`),!i)throw new Error("微信网页授权仅支持H5环境");s("验证微信配置...");const o=this.validateConfig();if(!o.valid)throw new Error(`微信配置错误: ${o.errors.join(", ")}`);s("微信配置验证通过");const n=this.generateState(e.extraState||{});s(`生成状态参数: ${n}`);const a=e.redirectUri||this.getRedirectUri(),c=e.scope||this.config.scope||"snsapi_userinfo";let d;if(s(`授权参数: state=${n}, redirectUri=${a}, scope=${c}`),e.useDirect)s("使用直接构造方式生成授权URL"),d=this.buildAuthUrlDirect({state:n,redirectUri:a,scope:c});else try{s("尝试通过后端接口获取授权URL"),d=await this.buildAuthUrl({state:n,redirectUri:a,scope:c,inviterId:e.inviterId})}catch(r){s(`后端接口获取授权URL失败，使用直接构造方式: ${r.message}`,"warning"),d=this.buildAuthUrlDirect({state:n,redirectUri:a,scope:c})}if(s(`最终授权URL: ${d}`),s("授权URL类型: "+typeof d),"string"!=typeof d)throw new Error("授权URL格式错误: "+typeof d+", 值: "+JSON.stringify(d));if(!d.startsWith("http"))throw new Error("授权URL格式无效: "+d);if(s("开始微信授权，准备跳转..."),"undefined"==typeof window)throw new Error("window对象不存在，无法执行跳转");{s("执行页面跳转..."),s(`跳转前当前URL: ${window.location.href}`),s("执行页面跳转...");let e=!1;try{window.location.href=d,s("方式1: location.href 已执行"),e=!0}catch(t){s(`方式1失败: ${t.message}`,"warning")}if(!e)try{window.location.assign(d),s("方式2: location.assign 已执行"),e=!0}catch(t){s(`方式2失败: ${t.message}`,"warning")}if(!e)try{window.location.replace(d),s("方式3: location.replace 已执行"),e=!0}catch(t){s(`方式3失败: ${t.message}`,"warning")}if(!e)try{window.open(d,"_self"),s("方式4: window.open 已执行")}catch(t){throw s(`方式4失败: ${t.message}`,"error"),new Error("所有跳转方式都失败了")}s("跳转命令已执行"),setTimeout((()=>{s(`跳转后当前URL: ${window.location.href}`),window.location.href.includes("open.weixin.qq.com")?s("跳转成功！","success"):s("跳转可能失败，URL未改变","warning")}),500)}}catch(r){throw s(`启动微信授权失败: ${r.message}`,"error"),s(`错误堆栈: ${r.stack}`,"error"),r}}handleCallback(e){try{const r=this.parseCallbackParams(e);if(r.error)return{success:!1,error:r.error,errorDescription:r.error_description,message:this.getErrorMessage(r.error)};if(!r.code)return{success:!1,error:"no_code",message:"未获取到授权码"};const t=this.parseState(r.state);return{success:!0,code:r.code,state:r.state,stateData:t}}catch(r){return console.error("处理授权回调失败:",r),{success:!1,error:"callback_error",message:"处理授权回调失败: "+r.message}}}getErrorMessage(e){return{access_denied:"用户拒绝授权",invalid_request:"请求参数错误",unauthorized_client:"客户端未授权",unsupported_response_type:"不支持的响应类型",invalid_scope:"无效的授权范围",server_error:"服务器错误",temporarily_unavailable:"服务暂时不可用"}[e]||`授权失败: ${e}`}validateConfig(){const e=[];return this.config.appId||e.push("缺少微信AppId配置"),this.config.authBaseUrl||e.push("缺少微信授权URL配置"),this.config.redirectUri&&(this.config.redirectUri.development||this.config.redirectUri.production)||e.push("缺少回调地址配置"),{valid:0===e.length,errors:e}}}const a=new n,c=()=>a.isH5Environment(),d=()=>a.isWechatBrowser(),h=e=>a.startAuth(e),l=e=>a.handleCallback(e);export{n as WechatWebAuth,a as default,l as handleWechatCallback,c as isH5Environment,d as isWechatBrowser,o as setLogCallback,h as startWechatAuth};
