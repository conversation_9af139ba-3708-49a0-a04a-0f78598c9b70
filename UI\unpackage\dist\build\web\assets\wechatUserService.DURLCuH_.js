function __vite__mapDeps(indexes) {
  if (!__vite__mapDeps.viteFileDeps) {
    __vite__mapDeps.viteFileDeps = ["assets/utils-wechatWebAuth.v7QTqDdJ.js","assets/index-Bl1z7-qC.js","assets/index-BYmBPzi2.css","assets/wechat.DoVyc6Fh.js"]
  }
  return indexes.map((i) => __vite__mapDeps.viteFileDeps[i])
}
import{a3 as e,O as t,M as r,N as s,a0 as a}from"./index-Bl1z7-qC.js";import{w as n}from"./video-user.DmBugx73.js";import{w as o,f as c}from"./wechat.DoVyc6Fh.js";const i="wechatUserInfo",u="wechatUserToken";const l=new class{async wechatLogin(e){try{const t=await n(e);if(t.success&&t.data){this.saveUserInfo(t.data.userInfo);const e=t.data.userToken||t.data.token;if(e&&this.saveUserToken(e),void 0!==t.data.auditStatus&&1!==t.data.auditStatus){const e=0===t.data.auditStatus?"待审核":"已拒绝";throw new Error(`用户审核状态：${e}，无法使用业务功能`)}return{success:!0,message:"微信登录成功",data:{...t.data,token:e}}}throw new Error(t.message||"微信登录失败")}catch(t){return console.error("微信登录失败:",t),{success:!1,message:t.message||"微信登录失败，请重试"}}}async tryWechatAutoLogin(e){try{return await this.tryWechatWebAuth(e)}catch(t){throw console.error("微信自动登录失败:",t),t}}async tryWechatWebAuth(t){try{const r=(await e((()=>import("./utils-wechatWebAuth.v7QTqDdJ.js")),__vite__mapDeps([0,1,2,3]))).default;if(!r.isH5Environment())throw new Error("当前环境不支持微信网页授权");const s={batchId:t.batchId?parseInt(t.batchId):null,returnUrl:t.returnUrl||"/pages/index/index"};return await r.startAuth({extraState:s,inviterId:t.sharerId||t.employeeId||null}),{success:!0,message:"正在跳转到微信授权页面..."}}catch(r){throw console.error("微信网页授权失败:",r),r}}async wechatWebLogin(e){try{const{code:t,state:r}=e,s=await o({code:t,state:r});if(s.success&&s.data){const e=c(s.data.userInfo||s.data.user);this.saveUserInfo(e);const t=s.data.userToken||s.data.token;if(t&&this.saveUserToken(t),void 0!==s.data.auditStatus&&1!==s.data.auditStatus){const e=0===s.data.auditStatus?"待审核":"已拒绝";throw new Error(`用户审核状态：${e}，无法使用业务功能`)}return{success:!0,message:"微信网页登录成功",data:{userInfo:e,token:t,auditStatus:s.data.auditStatus,isNewUser:s.data.isNewUser}}}throw new Error(s.msg||s.message||"微信网页登录失败")}catch(t){return console.error("微信网页登录失败:",t),{success:!1,message:t.message||"微信网页登录失败，请重试"}}}saveUserInfo(e){try{t(i,e)}catch(r){console.error("Error saving wechat user info:",r)}}saveUserToken(e){try{t(u,e)}catch(r){console.error("Error saving wechat user token:",r)}}getUserInfo(){try{return r(i)||null}catch(e){return console.error("Error getting wechat user info:",e),null}}getUserToken(){try{return r(u)||null}catch(e){return console.error("Error getting wechat user token:",e),null}}isLoggedIn(){const e=this.getUserInfo(),t=this.getUserToken();return!(!e||!t)}getUserId(){const e=this.getUserInfo();return(null==e?void 0:e.id)??null}getNickname(){const e=this.getUserInfo();return(null==e?void 0:e.nickname)??null}getAvatar(){const e=this.getUserInfo();return(null==e?void 0:e.avatar)??null}getOpenId(){const e=this.getUserInfo();return(null==e?void 0:e.openId)??null}getEmployeeId(){const e=this.getUserInfo();return(null==e?void 0:e.employeeId)??null}logout(){try{return s(i),s(u),console.log("微信用户已登出"),!0}catch(e){return console.error("Error during wechat user logout:",e),!1}}getDisplayInfo(){const e=this.getUserInfo();return e?{id:e.id,nickname:e.nickname||"微信用户",avatar:e.avatar||"/static/logo.png",openId:e.openId,employeeId:e.employeeId,userType:"wechat_user",createTime:e.createTime,lastLogin:e.lastLogin}:{nickname:"未登录",avatar:"/static/logo.png",userType:"guest"}}initMockWechatUser(){const e={id:"mock_wechat_user_001",nickname:"微信用户",avatar:"/static/logo.png",openId:"mock_openid_001",employeeId:null,createTime:(new Date).toISOString(),lastLogin:(new Date).toISOString()},t="mock_wechat_token_001";return this.saveUserInfo(e),this.saveUserToken(t),console.log("模拟微信用户初始化完成:",e.nickname),{userInfo:e,token:t}}redirectToLogin(e=""){const t=e?`/pages/user-login/index?returnUrl=${encodeURIComponent(e)}`:"/pages/user-login/index";a({url:t})}requireAuth(e={}){return!this.isLoggedIn()&&(console.log("微信用户未登录，需要认证"),!0)}},d=l,g=e=>l.wechatWebLogin(e);export{g as a,d as w};
