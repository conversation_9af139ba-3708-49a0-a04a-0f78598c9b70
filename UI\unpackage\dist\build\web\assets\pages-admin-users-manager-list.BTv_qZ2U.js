import{_ as a,a0 as e,c as s,w as n,i as t,o as r,h as l,j as o,l as d}from"./index-Bl1z7-qC.js";const u=a({name:"ManagerListRedirect",onLoad(){console.log("manager-list.vue 重定向到统一页面"),e({url:"/pages/admin/users/user-management?type=manager"})}},[["render",function(a,e,u,c,i,g){const m=d,f=t;return r(),s(f,{class:"redirect-page"},{default:n((()=>[l(f,{class:"loading"},{default:n((()=>[l(m,null,{default:n((()=>[o("正在跳转到管理员页面...")])),_:1})])),_:1})])),_:1})}],["__scopeId","data-v-01a5567f"]]);export{u as default};
