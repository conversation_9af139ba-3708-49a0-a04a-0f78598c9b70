import{m as n}from"./index-Bl1z7-qC.js";function r(r){return n.get("/Wechat/authorize",r)}function t(r){return n.get("/Wechat/callback",r)}function e(n){return n?{id:n.id||n.userId||"",nickname:n.nickname||n.nickName||"微信用户",avatar:n.avatar||n.headimgurl||"",openid:n.openid||"",unionid:n.unionid||"",gender:n.gender||0,city:n.city||"",province:n.province||"",country:n.country||"",language:n.language||"zh_CN"}:null}function a(n={}){const r={timestamp:Date.now(),random:Math.random().toString(36).substr(2,9),...n};try{return btoa(JSON.stringify(r))}catch(t){return console.error("构建状态参数失败:",t),""}}function o(n){try{if(!n)return null;const r=atob(n);return JSON.parse(r)}catch(r){return console.error("解析状态参数失败:",r),null}}export{a as b,e as f,r as g,o as p,t as w};
