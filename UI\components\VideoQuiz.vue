<template>
  <view class="quiz-box">
    <view class="quiz-header">
      <text class="quiz-title">视频问答</text>
      <text class="quiz-note" :class="{ ready: videoCompleted }">
        {{ videoCompleted ? "(可以答题了)" : "(请先观看完视频)" }}
      </text>
    </view>

    <!-- 问题列表 -->
    <view class="question-list">
      <view class="question-item" v-for="(question, qIndex) in questions" :key="qIndex">
        <view class="question-text">
          <text class="q-number">{{ qIndex + 1 }}. </text>
          <text class="q-content">{{ question.question }}</text>
        </view>

        <!-- 选项 -->
        <view class="options">
          <view class="option" v-for="option in question.options" :key="option.id" :class="{
            selected: selectedAnswers[qIndex] === option.id,
            correct: showResults && option.id === question.correctAnswer,
            wrong:
              showResults &&
              selectedAnswers[qIndex] === option.id &&
              option.id !== question.correctAnswer,
          }" @click="selectAnswer(qIndex, option.id)">
            <text class="option-label">{{ option.id }}</text>
            <text class="option-text">{{ option.text }}</text>
          </view>
        </view>


      </view>
    </view>

    <button class="submit-btn" :disabled="!canSubmit || !videoCompleted"
      :class="{ disabled: !canSubmit || !videoCompleted }" @click="submitAnswers">
      提交答案
    </button>

    <!-- 结果弹窗 -->
    <view class="result-modal" v-if="showResult">
      <view class="result-content">
        <view class="result-header">
          <text class="result-title">答题结果</text>
        </view>
        <text class="result-score">得分: {{ correctCount }}/{{ questions.length }}</text>
        <text class="result-reward" v-if="rewardAmount > 0">获得红包: {{ earnedAmount }}元</text>
        <button class="close-btn" @click="closeResult">关闭</button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: "VideoQuiz",
  props: {
    questions: {
      type: Array,
      default: () => [],
    },
    rewardAmount: {
      type: Number,
      default: 0,
    },
    videoCompleted: {
      type: Boolean,
      default: false,
    },
  },
  data () {
    return {
      selectedAnswers: [],
      showResults: false,
      correctCount: 0,
      showResult: false,
      earnedAmount: 0,
    };
  },
  computed: {
    canSubmit () {
      if (!this.questions || this.questions.length === 0) {
        return false;
      }

      // 检查是否所有问题都已回答
      return (
        this.selectedAnswers.length === this.questions.length &&
        !this.selectedAnswers.includes(undefined)
      );
    },
  },
  watch: {
    questions: {
      immediate: true,
      handler (newVal) {
        if (newVal && newVal.length > 0) {
          this.selectedAnswers = new Array(newVal.length);
        }
      },
    },
  },
  methods: {
    // 选择答案
    selectAnswer (questionIndex, optionId) {
      // 视频未完成不允许回答
      if (!this.videoCompleted) {
        uni.showToast({
          title: "请先观看完视频",
          icon: "none",
        });
        return;
      }

      // 已显示结果时不能再选择
      if (this.showResults) return;

      // 更新选择
      this.$set(this.selectedAnswers, questionIndex, optionId);

      // 如果所有问题都已回答，提示可以提交
      if (this.canSubmit) {
        uni.showToast({
          title: "已回答所有问题，可以提交",
          icon: "none",
          duration: 1500,
        });
      }
    },

    // 提交答案
    submitAnswers () {
      // 检查视频是否已完成
      if (!this.videoCompleted) {
        uni.showToast({
          title: "请先观看完视频",
          icon: "none",
        });
        return;
      }

      // 检查是否所有题目已答完
      if (!this.canSubmit) {
        const unanswered =
          this.questions.length - this.selectedAnswers.filter(Boolean).length;
        uni.showToast({
          title: `还有${unanswered}个问题未回答`,
          icon: "none",
        });
        return;
      }

      // 计算正确答案数
      this.correctCount = 0;
      const answerDetails = [];

      this.questions.forEach((question, index) => {
        const isCorrect = this.selectedAnswers[index] === question.correctAnswer;
        if (isCorrect) {
          this.correctCount++;
        }

        // 构造答题详情
        const selectedOption = question.options.find(opt => opt.id === this.selectedAnswers[index]);
        const correctOption = question.options.find(opt => opt.id === question.correctAnswer);

        answerDetails.push({
          questionOrderNum: index + 1,
          questionText: question.question,
          selectedOptionOrderNum: selectedOption ? selectedOption.orderNum || (question.options.indexOf(selectedOption) + 1) : 0,
          selectedOptionText: selectedOption ? selectedOption.text : '',
          correctOptionText: correctOption ? correctOption.text : '',
          isCorrect: isCorrect
        });
      });

      // 计算获得的奖励金额
      const percentage = this.correctCount / this.questions.length;
      this.earnedAmount = (this.rewardAmount * percentage).toFixed(2);

      // 显示结果
      this.showResults = true;

      // 通知父组件提交结果
      this.$emit("submit", {
        answers: this.selectedAnswers,
        correctCount: this.correctCount,
        earnedAmount: this.earnedAmount,
        totalQuestions: this.questions.length,
        answerDetails: answerDetails
      });

      // 延迟显示结果弹窗
      setTimeout(() => {
        this.showResult = true;
      }, 1000);
    },

    // 关闭结果弹窗
    closeResult () {
      this.showResult = false;

      // 通知父组件答题完成
      this.$emit("complete");
    },

    // 重置答案
    reset () {
      this.selectedAnswers = new Array(this.questions.length);
      this.showResults = false;
      this.correctCount = 0;
      this.showResult = false;
      this.earnedAmount = 0;
    },
  },
};
</script>

<style>
/* 问答区域 */
.quiz-box {
  background: white;
  padding: 20rpx;
  border-radius: 12rpx;
}

.quiz-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.quiz-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.quiz-note {
  font-size: 26rpx;
  color: #999;
  margin-left: 20rpx;
}

.quiz-note.ready {
  color: #186BFF;
}

/* 问题列表 */
.question-list {
  margin-bottom: 30rpx;
}

.question-item {
  margin-bottom: 30rpx;
}

.question-text {
  display: flex;
  margin-bottom: 16rpx;
}

.q-number {
  font-weight: bold;
  color: #333;
  margin-right: 10rpx;
}

.q-content {
  color: #333;
  flex: 1;
  line-height: 1.5;
}

/* 选项样式 */
.options {
  display: flex;
  flex-direction: column;
}

.option {
  display: flex;
  padding: 20rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  margin-bottom: 15rpx;
  border: 2rpx solid transparent;
}

.option-label {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background-color: #e0e0e0;
  color: #666;
  text-align: center;
  line-height: 40rpx;
  margin-right: 16rpx;
}

/* 选中状态 */
.option.selected {
  border-color: #186BFF;
  background-color: #e6f7ff;
}

.option.selected .option-label {
  background-color: #186BFF;
  color: white;
}

/* 正确答案 */
.option.correct {
  border-color: #52c41a;
  background-color: #f6ffed;
}

.option.correct .option-label {
  background-color: #52c41a;
  color: white;
}

/* 错误答案 */
.option.wrong {
  border-color: #f5222d;
  background-color: #fff1f0;
}

.option.wrong .option-label {
  background-color: #f5222d;
  color: white;
}



/* 提交按钮 */
.submit-btn {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  background-color: #186BFF;
  color: white;
  font-size: 30rpx;
  border-radius: 40rpx;
}

.submit-btn.disabled {
  background-color: #cccccc;
}

/* 结果弹窗 */
.result-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.result-content {
  width: 600rpx;
  background-color: white;
  border-radius: 20rpx;
  padding: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.result-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30rpx;
}

.result-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #186BFF;
}

.result-score {
  font-size: 48rpx;
  font-weight: bold;
  color: #186BFF;
  margin-bottom: 20rpx;
}

.result-reward {
  font-size: 36rpx;
  font-weight: bold;
  color: #ff5722;
  margin-bottom: 40rpx;
}

.close-btn {
  width: 80%;
  height: 80rpx;
  line-height: 80rpx;
  background-color: #186BFF;
  color: white;
  border-radius: 40rpx;
  font-size: 30rpx;
}
</style>