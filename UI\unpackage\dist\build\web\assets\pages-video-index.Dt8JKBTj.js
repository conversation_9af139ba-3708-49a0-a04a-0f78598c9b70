import{_ as e,v as t,o as s,c as o,w as r,h as i,j as n,n as a,t as c,A as d,B as l,F as h,k as u,l as m,i as w,C as g,D as p,E as I,G as f,H as v,I as b,J as y,K as C,L as T,M as A,N as k,O as P,q as U,u as _,x as D,P as S}from"./index-Bl1z7-qC.js";import{m as q}from"./media-common.7odKdPQQ.js";import{w as R}from"./wechatUserService.DURLCuH_.js";import"./video-user.DmBugx73.js";import"./wechat.DoVyc6Fh.js";const W={name:"VideoQuiz",props:{questions:{type:Array,default:()=>[]},rewardAmount:{type:Number,default:0},videoCompleted:{type:Boolean,default:!1}},data:()=>({selectedAnswers:[],showResults:!1,correctCount:0,showResult:!1,earnedAmount:0}),computed:{canSubmit(){return!(!this.questions||0===this.questions.length)&&(this.selectedAnswers.length===this.questions.length&&!this.selectedAnswers.includes(void 0))}},watch:{questions:{immediate:!0,handler(e){e&&e.length>0&&(this.selectedAnswers=new Array(e.length))}}},methods:{selectAnswer(e,s){this.videoCompleted?this.showResults||(this.$set(this.selectedAnswers,e,s),this.canSubmit&&t({title:"已回答所有问题，可以提交",icon:"none",duration:1500})):t({title:"请先观看完视频",icon:"none"})},submitAnswers(){if(!this.videoCompleted)return void t({title:"请先观看完视频",icon:"none"});if(!this.canSubmit){const e=this.questions.length-this.selectedAnswers.filter(Boolean).length;return void t({title:`还有${e}个问题未回答`,icon:"none"})}this.correctCount=0;const e=[];this.questions.forEach(((t,s)=>{const o=this.selectedAnswers[s]===t.correctAnswer;o&&this.correctCount++;const r=t.options.find((e=>e.id===this.selectedAnswers[s])),i=t.options.find((e=>e.id===t.correctAnswer));e.push({questionOrderNum:s+1,questionText:t.question,selectedOptionOrderNum:r?r.orderNum||t.options.indexOf(r)+1:0,selectedOptionText:r?r.text:"",correctOptionText:i?i.text:"",isCorrect:o})}));const s=this.correctCount/this.questions.length;this.earnedAmount=(this.rewardAmount*s).toFixed(2),this.showResults=!0,this.$emit("submit",{answers:this.selectedAnswers,correctCount:this.correctCount,earnedAmount:this.earnedAmount,totalQuestions:this.questions.length,answerDetails:e}),setTimeout((()=>{this.showResult=!0}),1e3)},closeResult(){this.showResult=!1,this.$emit("complete")},reset(){this.selectedAnswers=new Array(this.questions.length),this.showResults=!1,this.correctCount=0,this.showResult=!1,this.earnedAmount=0}}};async function x(e,t={}){console.log("微信用户认证失败:",e),R.logout();try{const e=await R.tryWechatAutoLogin(t);if(e.success)return console.log("微信重新授权成功"),!0;throw new Error(e.message||"重新授权失败")}catch(s){return console.error("微信重新授权失败:",s),I("登录已过期，请刷新页面重新登录"),!1}}async function E(e){const t=await function(e,t="提示"){return new Promise((s=>{b({title:t,content:e,confirmText:"确认",cancelText:"取消",success:e=>{s(e.confirm)},fail:()=>{s(!1)}})}))}("无法连接到服务器，是否重试？","连接错误");return t||I("已取消重试"),t}function O(e){return new Promise(((t,s)=>{const o=function(e){e.url.startsWith("http")||(e.url=f()+e.url),e.header={...v.DEFAULT_HEADERS,...e.header};const t=R.getUserToken();return t&&(e.header.Authorization=`Bearer ${t}`),e.timeout=e.timeout||v.TIMEOUT,e}(e);p({...o,success:o=>{(async function(e,t){const{data:s,statusCode:o}=e;if(200!==o)return 401===o?await x("认证失败",V.getPageOptions())?O(t):Promise.reject(new Error("认证失败")):o>=500?(I("服务器错误"),Promise.reject(new Error("服务器错误"))):(I(`请求失败 (${o})`),Promise.reject(new Error(`请求失败 (${o})`)));if(s&&"object"==typeof s){if("boolean"==typeof s.success){if(s.success)return Promise.resolve(s);{const e=s.msg||"请求失败";return 401===s.code?await x(e,V.getPageOptions())?O(t):Promise.reject(new Error(e)):Promise.resolve(s)}}const{code:e,msg:o,message:r}=s;return 200===e||0===e||500===e?Promise.resolve(s):401===e?await x(o||r||"认证失败",V.getPageOptions())?O(t):Promise.reject(new Error(o||r||"认证失败")):Promise.resolve(s)}return Promise.resolve(s)})(o,e).then(t).catch(s)},fail:async o=>{if(o.errMsg&&o.errMsg.includes("timeout"))I("请求超时，请检查网络连接"),s(o);else if(o.errMsg&&o.errMsg.includes("fail")){await E()?O(e).then(t).catch(s):s(o)}else I("请求配置错误: "+(o.errMsg||o.message||"未知错误")),s(o)}})}))}const V={get:(e,t={},s={})=>O({url:e,method:"GET",data:t,...s}),post:(e,t={},s={})=>O({url:e,method:"POST",data:t,...s}),put:(e,t={},s={})=>O({url:e,method:"PUT",data:t,...s}),delete:(e,t={},s={})=>O({url:e,method:"DELETE",data:t,...s}),setPageOptions(e){this._pageOptions=e},getPageOptions(){return this._pageOptions||{}}};const z=e({mixins:[q],components:{VideoQuiz:e(W,[["render",function(e,t,p,I,f,v){const b=m,y=w,C=g;return s(),o(y,{class:"quiz-box"},{default:r((()=>[i(y,{class:"quiz-header"},{default:r((()=>[i(b,{class:"quiz-title"},{default:r((()=>[n("视频问答")])),_:1}),i(b,{class:a(["quiz-note",{ready:p.videoCompleted}])},{default:r((()=>[n(c(p.videoCompleted?"(可以答题了)":"(请先观看完视频)"),1)])),_:1},8,["class"])])),_:1}),i(y,{class:"question-list"},{default:r((()=>[(s(!0),d(h,null,l(p.questions,((e,t)=>(s(),o(y,{class:"question-item",key:t},{default:r((()=>[i(y,{class:"question-text"},{default:r((()=>[i(b,{class:"q-number"},{default:r((()=>[n(c(t+1)+". ",1)])),_:2},1024),i(b,{class:"q-content"},{default:r((()=>[n(c(e.question),1)])),_:2},1024)])),_:2},1024),i(y,{class:"options"},{default:r((()=>[(s(!0),d(h,null,l(e.options,(d=>(s(),o(y,{class:a(["option",{selected:f.selectedAnswers[t]===d.id,correct:f.showResults&&d.id===e.correctAnswer,wrong:f.showResults&&f.selectedAnswers[t]===d.id&&d.id!==e.correctAnswer}]),key:d.id,onClick:e=>v.selectAnswer(t,d.id)},{default:r((()=>[i(b,{class:"option-label"},{default:r((()=>[n(c(d.id),1)])),_:2},1024),i(b,{class:"option-text"},{default:r((()=>[n(c(d.text),1)])),_:2},1024)])),_:2},1032,["class","onClick"])))),128))])),_:2},1024)])),_:2},1024)))),128))])),_:1}),i(C,{class:a(["submit-btn",{disabled:!v.canSubmit||!p.videoCompleted}]),disabled:!v.canSubmit||!p.videoCompleted,onClick:v.submitAnswers},{default:r((()=>[n(" 提交答案 ")])),_:1},8,["disabled","class","onClick"]),f.showResult?(s(),o(y,{key:0,class:"result-modal"},{default:r((()=>[i(y,{class:"result-content"},{default:r((()=>[i(y,{class:"result-header"},{default:r((()=>[i(b,{class:"result-title"},{default:r((()=>[n("答题结果")])),_:1})])),_:1}),i(b,{class:"result-score"},{default:r((()=>[n("得分: "+c(f.correctCount)+"/"+c(p.questions.length),1)])),_:1}),p.rewardAmount>0?(s(),o(b,{key:0,class:"result-reward"},{default:r((()=>[n("获得红包: "+c(f.earnedAmount)+"元",1)])),_:1})):u("",!0),i(C,{class:"close-btn",onClick:v.closeResult},{default:r((()=>[n("关闭")])),_:1},8,["onClick"])])),_:1})])),_:1})):u("",!0)])),_:1})}],["__scopeId","data-v-24652269"]])},data:()=>({videoId:2,batchId:null,sharerId:null,currentVideo:{},quizData:{},duration:0,isFullscreen:!1,videoCompleted:!1,maxWatchTime:0,currentPlayTime:0,progressTimer:null,watchStartTime:null,totalWatchTime:0,viewRecordId:null,recordCreated:!1,isCreatingRecord:!1,batchInfo:null}),async onLoad(e){try{e&&0!==Object.keys(e).length||(e=this.parseUrlParams()||{}),y({pageStyle:{overflow:"hidden"}}),e&&(e.id||e.videoId)&&(this.videoId=parseInt(e.id||e.videoId)),e&&e.batchId&&(this.batchId=parseInt(e.batchId)),e&&e.sharerId&&(this.sharerId=e.sharerId),V.setPageOptions(e),await this.ensureUserAuthenticated(e),await this.loadVideoData()}catch(s){console.error("页面初始化失败:",s),t({title:s.message||"页面加载失败",icon:"none",duration:3e3}),s.message&&s.message.includes("登录")&&setTimeout((()=>{C()}),3e3)}},onShow(){this.batchId||this.parseUrlParams();const e=T("myVideo",this);e&&e.play(),this.videoCompleted||this.startProgressMonitoring()},onHide(){const e=T("myVideo",this);e&&e.pause(),this.stopProgressMonitoring()},onUnload(){this.stopProgressMonitoring()},methods:{getLocalWatchRecord(){if(!this.batchId||!this.getCurrentUserId())return null;const e=`watch_record_${this.batchId}_${this.getCurrentUserId()}`,t=A(e);return t&&t.expireTime&&Date.now()>t.expireTime?(k(e),null):t},saveLocalWatchRecord(e){if(!this.batchId||!this.getCurrentUserId())return;const t=`watch_record_${this.batchId}_${this.getCurrentUserId()}`,s={id:e.id,batchId:this.batchId,userId:this.getCurrentUserId(),createTime:Date.now(),expireTime:Date.now()+864e5};P(t,s)},clearLocalWatchRecord(){if(!this.batchId||!this.getCurrentUserId())return;const e=`watch_record_${this.batchId}_${this.getCurrentUserId()}`;k(e)},async ensureUserAuthenticated(e){try{if(R.isLoggedIn())return void console.log("用户已登录:",R.getNickname());console.log("用户未登录，开始微信授权流程..."),U({title:"正在登录...",mask:!0});const s={employeeId:e.employeeId||null,batchId:e.batchId?parseInt(e.batchId):null,sharerId:e.sharerId||this.sharerId||null,returnUrl:"/pages/video/index"},o=await R.tryWechatAutoLogin(s);if(_(),!o.success)throw new Error(o.message||"微信登录失败");console.log("微信登录成功:",o.data.userInfo.nickname),t({title:"登录成功",icon:"success",duration:1500})}catch(s){throw _(),console.error("用户认证失败:",s),new Error("登录失败，请重试: "+s.message)}},async tryWechatAutoLogin(e){try{const t=await R.tryWechatAutoLogin(e);if(t.success)return console.log("微信自动登录成功:",t.data.userInfo.nickname),t.data;throw new Error(t.message||"微信登录失败")}catch(t){throw console.error("微信自动登录失败:",t),t}},async loadVideoData(){try{U({title:"加载视频中..."}),this.batchId?await this.loadDataFromBatch():await this.loadDataFromVideo(),await this.loadUserProgress(),this.sharerId&&this.recordSharerInfo(),_()}catch(e){console.error("加载视频数据失败:",e),_(),t({title:e.message||"加载失败",icon:"none"}),setTimeout((()=>{C()}),1500)}},async loadDataFromBatch(){console.log("从批次获取数据:",this.batchId);const e=await V.get(`/Batch/${this.batchId}`);if(!e.success||!e.data)throw new Error(e.msg||"获取批次详情失败");const t=e.data;console.log("=== 批次数据调试信息 ==="),console.log("API返回的原始批次数据:",t),this.validateBatchStatus(t),this.batchInfo=t;const s=this.buildCompleteFileUrl(t.videoUrl),o=this.buildCompleteFileUrl(t.videoCoverUrl);this.currentVideo={id:t.videoId,title:t.videoTitle,cover:o||"/assets/images/video-cover.jpg",url:s||"https://www.runoob.com/try/demo_source/mov_bbb.mp4",duration:t.videoDuration||0,views:0,likes:0,description:t.videoDescription||"",rewardAmount:t.rewardAmount||0},this.duration=t.videoDuration||0,this.processQuizData(t.questions),console.log("最终设置的currentVideo:",this.currentVideo),console.log("最终设置的quizData:",this.quizData),console.log("=== 批次数据调试结束 ===")},async loadDataFromVideo(){console.log("从视频API获取数据:",this.videoId);const e=await V.get(`/Video/Get/${this.videoId}`);if(!e.success||!e.data)throw new Error(e.msg||"获取视频详情失败");const t=e.data;console.log("=== 视频数据调试信息 ==="),console.log("API返回的原始视频数据:",t);const s=this.buildCompleteFileUrl(t.videoUrl),o=this.buildCompleteFileUrl(t.coverUrl);this.currentVideo={id:t.id,title:t.title,cover:o||"/assets/images/video-cover.jpg",url:s||"https://www.runoob.com/try/demo_source/mov_bbb.mp4",duration:t.duration||0,views:t.viewCount||0,likes:t.likeCount||0,description:t.description||"",rewardAmount:t.rewardAmount||0},this.duration=t.duration||0,this.processQuizData(t.questions),console.log("最终设置的currentVideo:",this.currentVideo),console.log("最终设置的quizData:",this.quizData),console.log("=== 视频数据调试结束 ===")},async loadUserProgress(){try{const e=await V.post("/UserBatchRecord/create-or-get",{batchId:this.batchId,userId:this.getCurrentUserId(),promotionLink:this.sharerId?`shared_by_${this.sharerId}`:null});if(e.success&&e.data){const t=e.data;this.totalWatchTime=t.viewDuration||0,this.maxWatchTime=this.duration>0?Math.round(t.watchProgress*this.duration):0,t.isCompleted&&(this.videoCompleted=!0,this.maxWatchTime=Number.MAX_VALUE),this.maxWatchTime>0&&b({title:"继续观看",content:`检测到您上次观看到 ${this.formatTime(this.maxWatchTime)}，是否从此处继续？`,success:e=>{e.confirm&&setTimeout((()=>{const e=T("myVideo",this);e&&e.seek(this.maxWatchTime)}),1e3)}})}}catch(e){console.error("加载用户进度失败:",e)}},processQuizData(e){if(!e||!Array.isArray(e)||0===e.length)return console.log("没有问题数据"),void(this.quizData={questions:[]});console.log("开始处理问题数据:",e);try{const t=e.map((e=>{const t=["A","B","C","D","E","F"],s=(e.options||[]).map(((e,s)=>({id:t[s]||s.toString(),text:e.optionText||e.text||""})));let o="";const r=(e.options||[]).findIndex((e=>e.isCorrect));return r>=0&&(o=t[r]||r.toString()),{question:e.questionText||e.question||"",options:s,correctAnswer:o}}));this.quizData={questions:t},console.log("问题数据处理完成:",this.quizData)}catch(t){console.error("处理问题数据失败:",t),this.quizData={questions:[]}}},validateBatchStatus(e){if(1!==e.status)throw new Error("批次未启用");const t=new Date,s=new Date(e.startTime),o=new Date(e.endTime);if(t<s)throw new Error("批次尚未开始");if(t>o)throw new Error("批次已结束");console.log("批次验证通过:",e)},formatTime:e=>`${Math.floor(e/60)}:${Math.floor(e%60).toString().padStart(2,"0")}`,onTimeUpdate(e){const s=e.detail||{},o=s.currentTime||0,r=s.duration||this.duration||0;if(this.currentPlayTime=o,r>0&&(this.duration=r),o>this.maxWatchTime+1){const e=T("myVideo",this);return e&&e.seek(this.maxWatchTime),void t({title:"请完整观看视频，不允许快进",icon:"none",duration:2e3})}o>this.maxWatchTime&&(this.maxWatchTime=o);const i={current:o,duration:r};this.reportProgress&&this.reportProgress(i)},onVideoEnded(){this.videoCompleted=!0,this.maxWatchTime=Number.MAX_VALUE,this.stopProgressMonitoring(),t({title:"视频播放完成，可以答题了",icon:"none",duration:2e3})},onSeeking(e){(e.detail.currentTime||0)>this.maxWatchTime+1&&(setTimeout((()=>{const e=T("myVideo",this);e&&e.seek(this.maxWatchTime)}),100),t({title:"不允许快进，请完整观看",icon:"none",duration:2e3}))},onSeeked(e){if((e.detail.currentTime||0)>this.maxWatchTime+1){const e=T("myVideo",this);e&&e.seek(this.maxWatchTime),t({title:"已回退到正确位置",icon:"none",duration:1500})}},onPlay(){this.watchStartTime=Date.now()},onPause(){if(this.watchStartTime){const e=(Date.now()-this.watchStartTime)/1e3;this.totalWatchTime+=e,this.watchStartTime=null}},onLoadedMetadata(){},onWaiting(){},onCanPlay(){},onFullscreenChange(e){this.isFullscreen=e.detail.fullScreen,this.isFullscreen?this.lockOrientation():this.unlockOrientation()},async lockOrientation(){try{if(screen.orientation&&screen.orientation.lock)return void(await screen.orientation.lock("landscape"));if(screen.lockOrientation)return void screen.lockOrientation("landscape");if(screen.webkitLockOrientation)return void screen.webkitLockOrientation("landscape");if(screen.mozLockOrientation)return void screen.mozLockOrientation("landscape")}catch(e){}},unlockOrientation(){try{if(screen.orientation&&screen.orientation.unlock)return void screen.orientation.unlock();if(screen.unlockOrientation)return void screen.unlockOrientation();if(screen.webkitUnlockOrientation)return void screen.webkitUnlockOrientation();if(screen.mozUnlockOrientation)return void screen.mozUnlockOrientation()}catch(e){}},async checkAnswerEligibility(){try{const e=await V.get(`/UserBatchRecord/${this.getCurrentUserId()}/answer-eligibility/${this.batchId}`);if(e.success&&e.data){if(!e.data.canAnswer)throw new Error(e.data.reason||"不符合答题条件");return!0}throw new Error(e.message||"检查答题资格失败")}catch(e){return console.error("检查答题资格失败:",e),t({title:e.message||"检查答题资格失败",icon:"none",duration:3e3}),!1}},async getAnswerStatus(){try{const e=await V.get(`/UserBatchRecord/${this.getCurrentUserId()}/answer-status/${this.batchId}`);if(e.success&&e.data)return e.data;throw new Error(e.message||"获取答题状态失败")}catch(e){return console.error("获取答题状态失败:",e),null}},async onQuizSubmit(e){try{if(!this.batchId)return void console.warn("缺少批次ID，无法提交答案");if(!(await this.checkAnswerEligibility()))return;console.log("提交答案:",e);const s={batchId:this.batchId,totalQuestions:e.totalQuestions,correctAnswers:e.correctCount,answerDetails:JSON.stringify(e.answerDetails)};console.log("提交数据:",s);const o=await V.post(`/UserBatchRecord/${this.getCurrentUserId()}/submit-answer`,s);o.success?(console.log("答案提交成功:",o.data),t({title:"答案提交成功",icon:"success"})):(console.error("答案提交失败:",o.msg),t({title:"答案提交失败",icon:"none"}))}catch(s){console.error("提交答案失败:",s),t({title:"提交失败，请重试",icon:"none"})}},async onQuizComplete(e){try{if(console.log("答题完成，奖励信息:",e),e&&e.rewardAmount>0){const s=await V.post(`/UserBatchRecord/${this.getCurrentUserId()}/grant-reward`,{batchId:this.batchId,rewardAmount:e.rewardAmount,transactionId:null,outTradeNo:null});s.success?(console.log("奖励发放成功:",s.data),t({title:`恭喜获得 ${e.rewardAmount} 元奖励！`,icon:"success",duration:3e3})):console.error("奖励发放失败:",s.msg)}else t({title:"答题完成！",icon:"success",duration:2e3});console.log("答题完成，用户可以继续观看视频或手动返回")}catch(s){console.error("处理答题完成失败:",s),t({title:"答题完成处理失败",icon:"none",duration:2e3})}},async startProgressMonitoring(){this.progressTimer&&clearInterval(this.progressTimer),this.watchStartTime=Date.now();const e=this.getLocalWatchRecord();if(this.recordCreated||e)e&&(this.recordCreated=!0,this.viewRecordId=e.id,console.log("使用已存在的观看记录:",this.viewRecordId));else{if(!(await this.createWatchRecord()))return void console.error("观看记录创建失败，无法启动进度监控")}this.recordCreated&&this.viewRecordId?(console.log("启动进度监控，记录ID:",this.viewRecordId),this.progressTimer=setInterval((()=>{this.outputWatchProgress()}),5e3)):console.error("观看记录未创建，无法启动进度监控")},stopProgressMonitoring(){if(this.watchStartTime){const e=(Date.now()-this.watchStartTime)/1e3;this.totalWatchTime+=e,this.watchStartTime=null}this.progressTimer&&(clearInterval(this.progressTimer),this.progressTimer=null),this.submitWatchProgress()},outputWatchProgress(){if(this.watchStartTime){const e=Date.now(),t=(e-this.watchStartTime)/1e3;this.totalWatchTime+=t,this.watchStartTime=e}this.submitWatchProgress()},async createWatchRecord(){if(!this.batchId)return console.warn("缺少批次ID，无法创建观看记录"),!1;if(this.isCreatingRecord)return!1;const e=this.getLocalWatchRecord();if(e&&e.batchId===this.batchId)return this.recordCreated=!0,this.viewRecordId=e.id,console.log("使用本地存储的观看记录:",this.viewRecordId),!0;if(this.recordCreated&&this.viewRecordId)return console.log("观看记录已存在:",this.viewRecordId),!0;this.isCreatingRecord=!0;try{let e="";this.sharerId&&(e=`shared_by_${this.sharerId}`),console.log("创建观看记录:",{batchId:this.batchId,userId:this.getCurrentUserId()});const t=await V.post("/UserBatchRecord/create-or-get",{batchId:this.batchId,userId:this.getCurrentUserId(),promotionLink:e});return t.success&&t.data?(this.viewRecordId=t.data.id,this.recordCreated=!0,this.saveLocalWatchRecord(t.data),console.log("观看记录创建成功:",this.viewRecordId),!0):(console.error("创建观看记录失败:",t.msg),!1)}catch(t){return console.error("创建观看记录异常:",t),!1}finally{this.isCreatingRecord=!1}},async submitWatchProgress(){if(this.batchId)if(this.recordCreated&&this.viewRecordId)try{let e=0;this.duration>0&&this.maxWatchTime>=0&&(e=Math.min(this.maxWatchTime/this.duration,1));const t={batchId:this.batchId,viewDuration:Math.floor(this.totalWatchTime),watchProgress:e,isCompleted:this.videoCompleted};console.log("提交观看进度:",t);const s=await V.post(`/UserBatchRecord/${this.getCurrentUserId()}/watch-progress`,t);s.success?console.log("观看进度更新成功"):console.error("观看进度更新失败:",s.msg)}catch(e){console.error("提交观看进度失败:",e)}else console.warn("观看记录未创建，无法更新进度");else console.warn("缺少批次ID，无法更新观看进度")},getCurrentUserId(){const e=R.getUserId();if(e)return e;throw console.error("用户未登录，无法获取用户ID"),new Error("用户未登录")},parseUrlParams(){try{const e=window.location.href.split("#")[1];if(e&&e.includes("?")){const t=e.split("?")[1],s=new URLSearchParams(t),o={};for(let[e,r]of s)o[e]=r;return o.videoId&&(this.videoId=parseInt(o.videoId)),o.batchId&&(this.batchId=parseInt(o.batchId)),o.sharerId&&(this.sharerId=o.sharerId),o}return null}catch(e){return console.error("解析URL参数失败:",e),null}},recordSharerInfo(){try{console.log("记录分享人信息:",this.sharerId),P("currentSharerInfo",{sharerId:this.sharerId,batchId:this.batchId,videoId:this.videoId,shareTime:(new Date).toISOString()})}catch(e){console.error("记录分享人信息失败:",e)}}}},[["render",function(e,t,a,d,l,h){const g=S,p=m,I=w,f=D("VideoQuiz");return s(),o(I,{class:"container"},{default:r((()=>[i(g,{id:"myVideo",ref:"videoPlayer",src:l.currentVideo.url,poster:l.currentVideo.cover,class:"video-player",controls:!0,autoplay:!0,"show-progress":!0,"show-fullscreen-btn":!0,"show-play-btn":!0,"show-center-play-btn":!0,"enable-progress-gesture":!1,"page-gesture":!1,direction:0,"show-mute-btn":!1,"enable-play-gesture":!1,onLoadedmetadata:h.onLoadedMetadata,onTimeupdate:h.onTimeUpdate,onEnded:h.onVideoEnded,onPlay:h.onPlay,onPause:h.onPause,onFullscreenchange:h.onFullscreenChange,onWaiting:h.onWaiting,onCanplay:h.onCanPlay,onSeeking:h.onSeeking,onSeeked:h.onSeeked},null,8,["src","poster","onLoadedmetadata","onTimeupdate","onEnded","onPlay","onPause","onFullscreenchange","onWaiting","onCanplay","onSeeking","onSeeked"]),l.isFullscreen?u("",!0):(s(),o(I,{key:0,class:"video-content"},{default:r((()=>[i(I,{class:"video-info"},{default:r((()=>[i(p,{class:"video-title"},{default:r((()=>[n(c(l.currentVideo.title),1)])),_:1}),i(I,{class:"video-meta"},{default:r((()=>[i(p,{class:"views"},{default:r((()=>[n(c(l.currentVideo.views)+"次观看",1)])),_:1})])),_:1}),i(p,{class:"description"},{default:r((()=>[n(c(l.currentVideo.description),1)])),_:1})])),_:1}),i(f,{questions:l.quizData.questions,rewardAmount:l.currentVideo.rewardAmount||0,videoCompleted:l.videoCompleted,onSubmit:h.onQuizSubmit,onComplete:h.onQuizComplete},null,8,["questions","rewardAmount","videoCompleted","onSubmit","onComplete"])])),_:1}))])),_:1})}],["__scopeId","data-v-c93307d9"]]);export{z as default};
