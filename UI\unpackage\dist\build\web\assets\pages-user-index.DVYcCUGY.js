import{a as e,I as s,Q as t,E as r,v as a,K as i,_ as n,q as o,u as l,R as c,S as u,T as d,p as m,r as g,b as h,c as p,w as f,U as P,i as U,V as y,e as _,W as T,X as b,o as k,h as v,j as S,t as I,k as L,A as x,B as D,F as q,l as w}from"./index-Bl1z7-qC.js";import{p as C}from"./permission-mixin.QQrgJw9R.js";function M(s={}){return function(s={},t=[],r=[]){if(!e.isLoggedIn())return e.redirectToLogin(),!1;if(!e.isSessionValid())return e.logout(),e.redirectToLogin(),!1;if(r.length>0){const s=e.getUserType();if(!r.includes(s))return a({title:"您没有权限访问此页面",icon:"none"}),setTimeout((()=>{i()}),1500),!1}if(t.length>0&&!t.every((s=>e.hasPermission(s))))return a({title:"您没有权限访问此功能",icon:"none"}),setTimeout((()=>{i()}),1500),!1;return e.refreshSession(),!0}(s,[],[])}function $(){if(!e.isLoggedIn())return{name:"未登录",username:"",userType:"",userTypeText:"游客",avatar:"",email:"",phone:"",department:"",position:""};const s=e.getLoginInfo();return{name:s.nickName||s.username||"未知用户",username:s.username||"",userType:s.userType||"",userTypeText:{employee:"员工",manager:"管理",admin:"超管"}[s.userType]||"未知",avatar:s.avatar||"",email:s.email||"",phone:s.phone||"",department:s.department||"",position:s.position||""}}async function R(s=!1){return await e.getCompleteUserInfo(s)}const z=n({mixins:[C],data:()=>({currentUser:{},loading:!1,mainPages:[{name:"数据统计",path:"/pages/index/index",icon:"icon-dashboard",requiredPermission:"view_dashboard"}],personalPages:[{name:"修改密码",path:"/pages/user/change-password",icon:"icon-lock",requiredPermission:null}],userPages:[{name:"管理信息",path:"/pages/admin/users/manager-list",icon:"icon-users",requiredRoles:["admin","super_admin"]},{name:"员工信息",path:"/pages/admin/users/employee-list",icon:"icon-staff",requiredRoles:["manager","agent","admin","super_admin"]},{name:"用户信息",path:"/pages/admin/users/user-list",icon:"icon-user",requiredPermission:"manage_users"},{name:"用户审核",path:"/pages/admin/users/audit/user-audit",icon:"icon-audit",requiredPermission:"user_audit"}]}),computed:{userTypeText(){return{employee:"员工",manager:"管理",admin:"超管",super_admin:"超管",agent:"管理"}[this.currentUser.userType]||"用户"},formatLastLoginTime(){if(!this.currentUser.lastLoginTime)return"从未登录";try{const e=new Date(this.currentUser.lastLoginTime),s=new Date;if(s-e<864e5&&e.getDate()===s.getDate())return`今天 ${e.getHours().toString().padStart(2,"0")}:${e.getMinutes().toString().padStart(2,"0")}`;const t=new Date(s);return t.setDate(t.getDate()-1),e.getDate()===t.getDate()&&e.getMonth()===t.getMonth()?`昨天 ${e.getHours().toString().padStart(2,"0")}:${e.getMinutes().toString().padStart(2,"0")}`:`${e.getFullYear()}-${(e.getMonth()+1).toString().padStart(2,"0")}-${e.getDate().toString().padStart(2,"0")} ${e.getHours().toString().padStart(2,"0")}:${e.getMinutes().toString().padStart(2,"0")}`}catch(e){return this.currentUser.lastLoginTime}},visibleMainPages(){return this.mainPages.filter((e=>this.hasPagePermission(e)))},visiblePersonalPages(){return this.personalPages.filter((e=>this.hasPagePermission(e)))},visibleUserPages(){return this.userPages.filter((e=>this.hasPagePermission(e)))}},onLoad(e){M(e)&&this.loadUserInfo()},methods:{hasPagePermission(e){return"admin"===this.currentUserType||"super_admin"===this.currentUserType||(!e.requiredPermission&&!e.requiredRoles||(e.requiredPermission?this.canUseFeature(e.requiredPermission):!!e.requiredRoles&&this.hasRole(e.requiredRoles)))},async loadUserInfo(){try{this.currentUser=$(),console.log("缓存的用户信息:",this.currentUser);const e=await R(!1);e&&(this.currentUser=e,console.log("完整的用户信息:",this.currentUser)),this.initPermissionData()}catch(e){console.error("加载用户信息失败:",e),this.currentUser=$(),this.initPermissionData()}},async refreshUserInfo(){if(!this.loading){this.loading=!0,o({title:"刷新用户信息..."});try{const e=await R(!0);e&&(this.currentUser=e,this.initPermissionData(),a({title:"用户信息已更新",icon:"success"}))}catch(e){console.error("刷新用户信息失败:",e),a({title:"刷新失败，请重试",icon:"none"})}finally{this.loading=!1,l()}}},copyUserId(){this.currentUser.userId?c({data:this.currentUser.userId.toString(),success:()=>{a({title:"用户ID已复制",icon:"success"})},fail:()=>{a({title:"复制失败",icon:"none"})}}):a({title:"用户ID不存在",icon:"none"})},navigateTo(e){u({url:e,fail:()=>{d({url:e,fail:()=>{m({url:e})}})}})},showLogoutConfirm(){!function(a="确定要退出登录吗？"){s({title:"确认退出",content:a,success:async s=>{if(s.confirm)try{await e.logout()?(t("已退出登录"),setTimeout((()=>{e.redirectToLogin()}),1500)):r("退出失败，请重试")}catch(a){r("退出失败，请重试")}}})}()},getIconName:e=>({"icon-dashboard":"grid","icon-video":"play-circle","icon-users":"account","icon-staff":"account-fill","icon-user":"account","icon-info":"info-circle","icon-audit":"checkmark-circle","icon-lock":"lock"}[e]||"more-circle")}},[["render",function(e,s,t,r,a,i){const n=g(h("u-avatar"),P),o=U,l=w,c=g(h("u-tag"),y),u=g(h("u-icon"),_),d=g(h("u-cell"),T),m=g(h("u-cell-group"),b);return k(),p(o,{class:"wx-center-container"},{default:f((()=>[v(o,{class:"user-header"},{default:f((()=>[v(m,{border:!1},{default:f((()=>[v(d,{border:!1,onClick:i.refreshUserInfo},{icon:f((()=>[v(o,{class:"avatar-container"},{default:f((()=>[v(n,{src:a.currentUser.avatar||"/assets/images/avatar-placeholder.png",size:"60",shape:"square"},null,8,["src"])])),_:1})])),title:f((()=>[v(o,{class:"user-info"},{default:f((()=>[v(o,{class:"user-name"},{default:f((()=>[S(I(a.currentUser.realName||a.currentUser.name||"用户"),1)])),_:1}),v(o,{class:"user-meta"},{default:f((()=>[v(l,{class:"user-phone"},{default:f((()=>[S(I(a.currentUser.phone||a.currentUser.username||""),1)])),_:1}),v(c,{text:i.userTypeText,type:"primary",size:"mini",plain:!0},null,8,["text"])])),_:1}),a.currentUser.email||a.currentUser.department?(k(),p(o,{key:0,class:"user-extra-info"},{default:f((()=>[a.currentUser.email?(k(),p(l,{key:0,class:"user-email"},{default:f((()=>[S(I(a.currentUser.email),1)])),_:1})):L("",!0),a.currentUser.department?(k(),p(l,{key:1,class:"user-department"},{default:f((()=>[S(I(a.currentUser.department),1)])),_:1})):L("",!0)])),_:1})):L("",!0)])),_:1})])),"right-icon":f((()=>[v(u,{name:"arrow-right",color:"#c0c4cc",size:"16"})])),_:1},8,["onClick"])])),_:1})])),_:1}),i.visibleMainPages.length>0?(k(),p(o,{key:0,class:"menu-section"},{default:f((()=>[v(m,{border:!1},{default:f((()=>[(k(!0),x(q,null,D(i.visibleMainPages,((e,s)=>(k(),p(d,{key:s,title:e.name,isLink:!0,border:s!==i.visibleMainPages.length-1,onClick:s=>i.navigateTo(e.path)},{icon:f((()=>[v(u,{name:i.getIconName(e.icon),size:"20",color:"#666"},null,8,["name"])])),_:2},1032,["title","border","onClick"])))),128))])),_:1})])),_:1})):L("",!0),i.visiblePersonalPages.length>0?(k(),p(o,{key:1,class:"menu-section"},{default:f((()=>[v(m,{border:!1},{default:f((()=>[(k(!0),x(q,null,D(i.visiblePersonalPages,((e,s)=>(k(),p(d,{key:s,title:e.name,isLink:!0,border:s!==i.visiblePersonalPages.length-1,onClick:s=>i.navigateTo(e.path)},{icon:f((()=>[v(u,{name:i.getIconName(e.icon),size:"20",color:"#666"},null,8,["name"])])),_:2},1032,["title","border","onClick"])))),128))])),_:1})])),_:1})):L("",!0),i.visibleUserPages.length>0?(k(),p(o,{key:2,class:"menu-section"},{default:f((()=>[v(m,{border:!1},{default:f((()=>[(k(!0),x(q,null,D(i.visibleUserPages,((e,s)=>(k(),p(d,{key:s,title:e.name,isLink:!0,border:s!==i.visibleUserPages.length-1,onClick:s=>i.navigateTo(e.path)},{icon:f((()=>[v(u,{name:i.getIconName(e.icon),size:"20",color:"#666"},null,8,["name"])])),_:2},1032,["title","border","onClick"])))),128))])),_:1})])),_:1})):L("",!0),v(o,{class:"logout-section"},{default:f((()=>[v(m,{border:!1},{default:f((()=>[v(d,{title:"退出登录",isLink:!0,border:!1,onClick:i.showLogoutConfirm},{title:f((()=>[v(l,{class:"logout-text"},{default:f((()=>[S("退出登录")])),_:1})])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1})}],["__scopeId","data-v-2e0ff0b0"]]);export{z as default};
