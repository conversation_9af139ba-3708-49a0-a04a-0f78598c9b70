import{q as o,u as r,E as t}from"./index-Bl1z7-qC.js";async function i(i,a={}){const{fallback:s=null,loadingTitle:n="加载中...",errorTitle:e="加载失败",showLoading:l=!0,showError:w=!0}=a;try{l&&o({title:n});const t=await i();if(l&&r(),t&&t.success)return t;throw new Error((null==t?void 0:t.msg)||"API调用失败")}catch(c){if(l&&r(),w&&t(e),s&&"function"==typeof s)try{return await s()}catch(h){throw h}throw c}}const a={silent:{showLoading:!1,showError:!1},quick:{loadingTitle:"加载中...",showLoading:!0,showError:!0},important:{loadingTitle:"处理中，请稍候...",errorTitle:"操作失败，请重试",showLoading:!0,showError:!0},background:{showLoading:!1,showError:!1,retry:!0,maxRetries:2}};export{a as E,i as a};
