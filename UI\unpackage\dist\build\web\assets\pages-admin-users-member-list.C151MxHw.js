import{_ as e,S as a,K as s,Z as t,v as r,R as o,I as i,r as l,b as n,x as d,c as m,w as c,i as u,e as g,f as h,ab as f,V as p,ac as y,ad as w,d as I,ae as b,af as _,ag as T,ah as F,o as v,h as S,A as k,j as x,t as C,ai as M,k as P,B as U,F as D,l as A,a5 as E}from"./index-Bl1z7-qC.js";import{U as z}from"./UserInfoCard.MVeS5fVN.js";import{T as B}from"./TimeFilter.DV_809cK.js";import{g as $,a as R,r as V,t as j}from"./sysuser.DuFR7T6Y.js";import{g as L}from"./employee.BHNVchKC.js";import{b as K}from"./video-user.DmBugx73.js";import{f as q,a as N,b as O}from"./employee-data-mapper.B3YJtEOo.js";import{a as W,E as Z}from"./api-error-handler.uLKdJVtJ.js";import{m as G,f as H}from"./media-common.7odKdPQQ.js";import"./permission-mixin.QQrgJw9R.js";const J=e({mixins:[G],components:{UserInfoCard:z,TimeFilter:B},data(){return{managerId:null,managerType:"",managerInfo:{id:"",username:"加载中...",avatar:"",registerTime:"",lastLoginTime:"",managerId:"",disabled:!1,dismissed:!1,type:""},employees:[],users:[],searchKeyword:"",activeTimeFilter:"today",customDateRange:{startDate:"",endDate:""},currentUserRole:"admin",showDismissedEmployees:!1,showPasswordModal:!1,passwordForm:{newPassword:"",confirmPassword:""},passwordRules:{newPassword:[{required:!0,message:"请输入新密码",trigger:["blur","change"]},{min:6,max:20,message:"密码长度在 6 到 20 个字符",trigger:["blur","change"]},{pattern:/^(?=.*[a-zA-Z])(?=.*\d)/,message:"密码必须包含字母和数字",trigger:["blur","change"]}],confirmPassword:[{required:!0,message:"请确认密码",trigger:["blur","change"]},{validator:(e,a,s)=>{a!==this.passwordForm.newPassword?s(new Error("两次输入的密码不一致")):s()},trigger:["blur","change"]}]},currentUser:null,showManagerActionSheetVisible:!1,loading:!1}},computed:{pageTitle(){return"manager"===this.managerType?`${this.managerInfo.username}的成员`:`${this.managerInfo.username}的用户`},managerActionList(){const e=[{name:this.managerInfo.disabled?"启用账号":"禁用账号",value:"toggleStatus",color:this.managerInfo.disabled?"#52c41a":"#ff4d4f"},{name:"重置密码",value:"resetPassword",color:"#1890ff"}];return"employee"===this.managerType&&(e.push({name:this.managerInfo.dismissed?"恢复在职":"员工离职",value:"toggleDismiss",color:this.managerInfo.dismissed?"#52c41a":"#faad14"}),e.push({name:"用户转移",value:"transferUser",color:"#722ed1"})),e},allEmployees(){if("manager"!==this.managerType||!this.employees.length)return[];let e=this.employees.filter((e=>e.managerId===this.managerId));if(this.searchKeyword){const a=this.searchKeyword.toLowerCase();e=e.filter((e=>e.username.toLowerCase().includes(a)||e.phone&&e.phone.includes(a)))}return e},activeEmployees(){return this.allEmployees.filter((e=>!e.dismissed))},dismissedEmployees(){return this.allEmployees.filter((e=>e.dismissed))},filteredUsers(){let e=this.users;if("manager"===this.managerType){const a=this.employees.filter((e=>e.managerId===this.managerId)).map((e=>e.id));e=e.filter((e=>a.includes(e.employeeId)))}else"employee"===this.managerType&&(e=e.filter((e=>e.employeeId===this.managerId)));if(!this.searchKeyword)return e;const a=this.searchKeyword.toLowerCase();return e.filter((e=>e.username.toLowerCase().includes(a)||e.phone&&e.phone.includes(a)))}},async onLoad(e){e.id&&(this.managerId=e.id,this.managerType=e.type||"employee"),await this.loadManagerInfo(),await this.loadSubordinates(),this.currentUserRole=this.managerType},methods:{async loadManagerInfo(){if(this.managerId)try{let a;if("manager"===this.managerType)a=await W((()=>$(this.managerId)),{...Z.important,loadingTitle:"加载管理者信息...",errorTitle:"加载失败"}),a.success&&a.data&&(this.managerInfo=q(a.data),this.managerInfo.type=this.managerType);else try{a=await W((()=>$(this.managerId)),{...Z.silent}),a.success&&a.data&&(this.managerInfo=q(a.data),this.managerInfo.type=this.managerType)}catch(e){console.log("SysUser API 失败，尝试 Employee API"),a=await W((()=>L(this.managerId)),{...Z.important,loadingTitle:"加载员工信息...",errorTitle:"加载失败"}),a.success&&a.data&&(this.managerInfo=N(a.data),this.managerInfo.type=this.managerType)}}catch(a){console.error("加载管理者信息失败:",a),this.managerInfo={id:this.managerId||"",username:"加载失败",avatar:"",registerTime:"",lastLoginTime:"",managerId:"",disabled:!1,dismissed:!1,type:this.managerType}}},async loadSubordinates(){try{if(!this.managerId)return;"manager"===this.managerType?await this.loadEmployees():await this.loadUsers()}catch(e){console.error("加载下级成员失败:",e),this.employees=[],this.users=[]}},async loadEmployees(){try{const e=await W((()=>R(this.managerId,{pageIndex:1,pageSize:100})),{...Z.silent});e.success&&e.data&&(this.employees=e.data.map((e=>q(e))))}catch(e){console.error("加载员工列表失败:",e)}},async loadUsers(){try{const e=await W((()=>K(this.managerId,{PageIndex:1,PageSize:100})),{...Z.silent});e.success&&e.data&&(this.users=e.data.items.map((e=>O(e))))}catch(e){console.error("加载用户列表失败:",e)}},handleTimeFilterChange(e){this.activeTimeFilter=e,this.customDateRange={startDate:e.startDate,endDate:e.endDate},console.log("时间筛选变化:",e)},formattedUserInfo(e){let a="未知员工";if(e.employeeId)if("employee"===this.managerType&&e.employeeId===this.managerId)a=this.managerInfo.username||"当前员工";else{const s=this.employees.find((a=>a.id===e.employeeId));a=s?s.username:`员工${e.employeeId}`}return{...e,type:"user",employeeName:a,disabled:e.disabled||0===e.status||!1}},formattedEmployeeInfo(e){let a="未知管理";return e.managerId&&(a="manager"===this.managerType&&e.managerId===this.managerId?this.managerInfo.username||"当前管理":`管理${e.managerId}`),{...e,type:"employee",managerName:a,disabled:e.disabled||0===e.status||!1}},viewUserDetail(e){a({url:`/pages/admin/users/info?userId=${e.id}`})},viewEmployeeDetail(e){a({url:`/pages/admin/users/member-list?id=${e.id}&type=employee`})},goBack(){s()},handleChangePassword(e){console.log("修改密码:",e),this.currentUser=e,this.showPasswordModal=!0},closePasswordModal(){this.showPasswordModal=!1,this.passwordForm={newPassword:"",confirmPassword:""},this.currentUser=null,this.$nextTick((()=>{this.$refs.passwordForm&&this.$refs.passwordForm.resetFields()}))},async confirmResetPassword(){try{if(!(await this.$refs.passwordForm.validate()))return}catch(e){return}try{this.loading=!0;const e=t.MD5(this.passwordForm.newPassword).toString();await V({userId:this.currentUser.id,newPassword:e}),r({title:"密码重置成功",icon:"success"}),this.closePasswordModal()}catch(e){r({title:e.message||"密码重置失败",icon:"none"})}finally{this.loading=!1}},copyManagerId(){this.managerInfo.id&&o({data:this.managerInfo.id.toString(),success:()=>{r({title:"ID已复制",icon:"success"})}})},getManagerName:e=>e?"未知管理":"未分配",formatDate:e=>H(e,"datetime"),showManagerActionSheet(){this.showManagerActionSheetVisible=!0},closeManagerActionSheet(){this.showManagerActionSheetVisible=!1},handleManagerAction(e){switch(this.closeManagerActionSheet(),e.value){case"toggleStatus":this.handleManagerToggleStatus();break;case"resetPassword":this.handleManagerResetPassword();break;case"toggleDismiss":this.handleEmployeeDismiss();break;case"transferUser":this.handleUserTransfer()}},async handleManagerToggleStatus(){this.closeManagerActionSheet();const e=this.managerInfo.disabled?"启用":"禁用",a=this.managerInfo.disabled?1:0,s="manager"===this.managerType?"管理":"员工";i({title:`确认${e}`,content:`确定要${e}${s} ${this.managerInfo.username} 的账号吗？`,success:async s=>{if(s.confirm)try{await j(this.managerInfo.id,a),this.managerInfo.disabled=!this.managerInfo.disabled,r({title:`账号已${e}`,icon:"success"})}catch(t){r({title:t.message||`${e}失败`,icon:"none"})}}})},handleManagerResetPassword(){this.closeManagerActionSheet(),this.currentUser=this.managerInfo,this.showPasswordModal=!0},handleEmployeeDismiss(){this.closeManagerActionSheet(),this.managerInfo.dismissed?i({title:"确认恢复",content:`确定要恢复员工 ${this.managerInfo.username} 的在职状态吗？`,success:e=>{if(e.confirm){this.managerInfo.dismissed=!1;const e=this.employees.findIndex((e=>e.id===this.managerId));-1!==e&&(this.employees[e].dismissed=!1),r({title:"员工已恢复在职",icon:"success"})}}}):i({title:"确认离职",content:`确定要将员工 ${this.managerInfo.username} 设为离职状态吗？`,success:e=>{e.confirm&&(this.managerInfo.dismissed=!0,r({title:"员工已设为离职",icon:"success"}))}})},handleUserTransfer(){this.closeManagerActionSheet(),r({title:"用户转移功能开发中",icon:"none"})}}},[["render",function(e,a,s,t,r,o){const i=u,z=A,B=l(n("u-icon"),g),$=l(n("u-button"),h),R=l(n("u-alert"),f),V=d("TimeFilter"),j=d("UserInfoCard"),L=l(n("u-tag"),p),K=l(n("u-collapse-item"),y),q=l(n("u-collapse"),w),N=l(n("u-input"),I),O=l(n("u-form-item"),b),W=l(n("u-form"),_),Z=l(n("u-modal"),T),G=l(n("u-action-sheet"),F),H=E;return v(),m(i,{class:"container"},{default:c((()=>[S(i,{class:"manager-card fixed-header"},{default:c((()=>[S(i,{class:"custom-card"},{default:c((()=>[S(i,{class:"manager-header"},{default:c((()=>[S(i,{class:"manager-avatar-section"},{default:c((()=>[r.managerInfo.avatar?(v(),k("img",{key:0,src:e.buildCompleteFileUrl(r.managerInfo.avatar),style:{width:"60px",height:"60px","border-radius":"8px","object-fit":"cover"}},null,8,["src"])):(v(),m(i,{key:1,class:"avatar-placeholder",style:{width:"60px",height:"60px","border-radius":"8px","background-color":"#f0f9ff",color:"#186BFF",display:"flex","align-items":"center","justify-content":"center","font-size":"24px","font-weight":"bold"}},{default:c((()=>{var e,a;return[x(C((null==(a=null==(e=r.managerInfo.username)?void 0:e.charAt(0))?void 0:a.toUpperCase())||"U"),1)]})),_:1}))])),_:1}),S(i,{class:"manager-basic-info"},{default:c((()=>[S(i,{class:"manager-name-row"},{default:c((()=>[S(z,{class:"manager-name"},{default:c((()=>[x(C(r.managerInfo.username),1)])),_:1}),S($,{type:"primary",size:"mini",shape:"circle",onClick:M(o.copyManagerId,["stop"]),customStyle:{marginLeft:"12rpx",minWidth:"auto",height:"48rpx",padding:"0 16rpx"}},{default:c((()=>[S(B,{name:"copy",size:"12",color:"#fff"})])),_:1},8,["onClick"])])),_:1}),S(i,{class:"manager-actions"},{default:c((()=>[S($,{type:"primary",size:"small",shape:"round",onClick:M(o.showManagerActionSheet,["stop"]),text:"管理操作",customStyle:{marginTop:"16rpx"}},null,8,["onClick"])])),_:1})])),_:1})])),_:1}),S(i,{class:"manager-details"},{default:c((()=>[S(i,{class:"detail-grid"},{default:c((()=>[S(i,{class:"detail-item"},{default:c((()=>[S(B,{name:"calendar",size:"20",color:"#186BFF"}),S(i,{class:"detail-content"},{default:c((()=>[S(z,{class:"detail-label"},{default:c((()=>[x("注册时间")])),_:1}),S(z,{class:"detail-value"},{default:c((()=>[x(C(o.formatDate(r.managerInfo.registerTime)),1)])),_:1})])),_:1})])),_:1}),S(i,{class:"detail-item"},{default:c((()=>[S(B,{name:"clock",size:"20",color:"#186BFF"}),S(i,{class:"detail-content"},{default:c((()=>[S(z,{class:"detail-label"},{default:c((()=>[x("最后登录")])),_:1}),S(z,{class:"detail-value"},{default:c((()=>[x(C(o.formatDate(r.managerInfo.lastLoginTime)||"从未登录"),1)])),_:1})])),_:1})])),_:1}),"manager"===r.managerType?(v(),m(i,{key:0,class:"detail-item"},{default:c((()=>[S(B,{name:"account",size:"20",color:"#186BFF"}),S(i,{class:"detail-content"},{default:c((()=>[S(z,{class:"detail-label"},{default:c((()=>[x("管理员工")])),_:1}),S(z,{class:"detail-value"},{default:c((()=>[x(C(o.activeEmployees.length)+"人",1)])),_:1})])),_:1})])),_:1})):P("",!0),"manager"===r.managerType?(v(),m(i,{key:1,class:"detail-item"},{default:c((()=>[S(B,{name:"account-fill",size:"20",color:"#186BFF"}),S(i,{class:"detail-content"},{default:c((()=>[S(z,{class:"detail-label"},{default:c((()=>[x("总用户数")])),_:1}),S(z,{class:"detail-value"},{default:c((()=>[x(C(o.filteredUsers.length)+"人",1)])),_:1})])),_:1})])),_:1})):P("",!0),"employee"===r.managerType?(v(),m(i,{key:2,class:"detail-item"},{default:c((()=>[S(B,{name:"level",size:"20",color:"#186BFF"}),S(i,{class:"detail-content"},{default:c((()=>[S(z,{class:"detail-label"},{default:c((()=>[x("推广用户")])),_:1}),S(z,{class:"detail-value"},{default:c((()=>[x(C(o.filteredUsers.length)+"人",1)])),_:1})])),_:1})])),_:1})):P("",!0),"employee"===r.managerType?(v(),m(i,{key:3,class:"detail-item"},{default:c((()=>[S(B,{name:"man",size:"20",color:"#186BFF"}),S(i,{class:"detail-content"},{default:c((()=>[S(z,{class:"detail-label"},{default:c((()=>[x("所属管理")])),_:1}),S(z,{class:"detail-value"},{default:c((()=>[x(C(o.getManagerName(r.managerInfo.managerId)),1)])),_:1})])),_:1})])),_:1})):P("",!0)])),_:1})])),_:1})])),_:1})])),_:1}),"employee"===r.managerType&&r.managerInfo&&r.managerInfo.dismissed&&o.filteredUsers.length>0?(v(),m(R,{key:0,type:"warning","show-icon":!0,closable:!1,title:"该员工已离职，建议将用户转移给其他员工",description:`当前有 ${o.filteredUsers.length} 个用户需要转移`,customStyle:{margin:"20rpx"}},{icon:c((()=>[S(B,{name:"error-circle",size:"20",color:"#faad14"})])),desc:c((()=>[S(i,{style:{"margin-top":"16rpx"}},{default:c((()=>[S($,{type:"warning",size:"small",shape:"round",onClick:e.showTransferModal,text:"立即转移",customStyle:{marginTop:"16rpx"}},{default:c((()=>[S(B,{name:"reload",size:"14",color:"#fff",style:{"margin-right":"8rpx"}})])),_:1},8,["onClick"])])),_:1})])),_:1},8,["description"])):P("",!0),S(H,{class:"scrollable-content","scroll-y":"true"},{default:c((()=>[S(V,{modelValue:r.activeTimeFilter,"onUpdate:modelValue":a[0]||(a[0]=e=>r.activeTimeFilter=e),onChange:o.handleTimeFilterChange},null,8,["modelValue","onChange"]),"manager"===r.managerType&&o.activeEmployees.length>0?(v(),m(i,{key:0,class:"member-list"},{default:c((()=>[S(i,{class:"member-cards-container"},{default:c((()=>[(v(!0),k(D,null,U(o.activeEmployees,(e=>(v(),m(i,{class:"member-card",key:e.id,onClick:a=>o.viewEmployeeDetail(e)},{default:c((()=>[S(j,{userInfo:o.formattedEmployeeInfo(e),timeFilter:r.activeTimeFilter,customDateRange:r.customDateRange,showDetailBtn:!1,showFooterBtns:!1},null,8,["userInfo","timeFilter","customDateRange"])])),_:2},1032,["onClick"])))),128))])),_:1}),o.dismissedEmployees.length>0?(v(),m(q,{key:0,customStyle:{margin:"20rpx 0"}},{default:c((()=>[S(K,{title:`已离职员工 (${o.dismissedEmployees.length})`,name:"dismissed",icon:{name:"account",color:"#909399"}},{default:c((()=>[S(i,{class:"dismissed-content"},{default:c((()=>[(v(!0),k(D,null,U(o.dismissedEmployees,(e=>(v(),m(i,{class:"member-card dismissed-card",key:e.id,onClick:a=>o.viewEmployeeDetail(e)},{default:c((()=>[S(j,{userInfo:o.formattedEmployeeInfo(e),timeFilter:r.activeTimeFilter,customDateRange:r.customDateRange,showDetailBtn:!1,showFooterBtns:!1},null,8,["userInfo","timeFilter","customDateRange"]),S(L,{text:"已离职",type:"warning",shape:"circle",size:"mini",customStyle:{position:"absolute",top:"16rpx",right:"16rpx",zIndex:10}},{icon:c((()=>[S(B,{name:"error-circle",size:"12",color:"#faad14"})])),_:1})])),_:2},1032,["onClick"])))),128))])),_:1})])),_:1},8,["title"])])),_:1})):P("",!0)])),_:1})):P("",!0),"employee"===r.managerType?(v(),m(i,{key:1,class:"member-list"},{default:c((()=>[S(i,{class:"member-cards-container"},{default:c((()=>[(v(!0),k(D,null,U(o.filteredUsers,(e=>(v(),m(i,{class:"member-card",key:e.id,onClick:a=>o.viewUserDetail(e)},{default:c((()=>[S(j,{userInfo:o.formattedUserInfo(e),showDetailBtn:!1,showFooterBtns:!1},null,8,["userInfo"])])),_:2},1032,["onClick"])))),128))])),_:1})])),_:1})):P("",!0),S(Z,{show:r.showPasswordModal,"onUpdate:show":a[3]||(a[3]=e=>r.showPasswordModal=e),title:"重置密码",closeOnClickOverlay:!0,onClose:o.closePasswordModal,customStyle:{maxWidth:"600rpx"},showCancelButton:!1,showConfirmButton:!1},{default:c((()=>[S(i,{class:"modal-form"},{default:c((()=>[S(R,{type:"warning","show-icon":!0,closable:!1,title:"重置密码将覆盖用户当前密码，请谨慎操作",customStyle:{marginBottom:"32rpx"}}),S(W,{model:r.passwordForm,rules:r.passwordRules,ref:"passwordForm",labelWidth:"120",labelPosition:"top"},{default:c((()=>[S(O,{label:"新密码",prop:"newPassword",required:!0},{default:c((()=>[S(N,{modelValue:r.passwordForm.newPassword,"onUpdate:modelValue":a[1]||(a[1]=e=>r.passwordForm.newPassword=e),placeholder:"请输入新密码",type:"password",border:"surround",clearable:"",customStyle:{width:"100%"}},null,8,["modelValue"])])),_:1}),S(O,{label:"确认密码",prop:"confirmPassword",required:!0},{default:c((()=>[S(N,{modelValue:r.passwordForm.confirmPassword,"onUpdate:modelValue":a[2]||(a[2]=e=>r.passwordForm.confirmPassword=e),placeholder:"请再次输入新密码",type:"password",border:"surround",clearable:"",customStyle:{width:"100%"}},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules"]),S(i,{class:"password-tips"},{default:c((()=>[S(z,{class:"tip-title"},{default:c((()=>[x("密码要求：")])),_:1}),S(z,{class:"tip-item"},{default:c((()=>[x("• 长度不少于6位")])),_:1}),S(z,{class:"tip-item"},{default:c((()=>[x("• 必须包含字母和数字")])),_:1}),S(z,{class:"tip-item"},{default:c((()=>[x("• 建议包含特殊字符")])),_:1})])),_:1}),S(i,{class:"modal-buttons"},{default:c((()=>[S($,{type:"info",onClick:o.closePasswordModal,text:"取消",size:"large",customStyle:{width:"45%",marginRight:"10%"}},null,8,["onClick"]),S($,{type:"primary",onClick:o.confirmResetPassword,loading:r.loading,text:"确认重置",size:"large",customStyle:{width:"45%"}},null,8,["onClick","loading"])])),_:1})])),_:1})])),_:1},8,["show","onClose"]),S(G,{show:r.showManagerActionSheetVisible,"onUpdate:show":a[4]||(a[4]=e=>r.showManagerActionSheetVisible=e),actions:o.managerActionList,title:"管理操作",onSelect:o.handleManagerAction,onClose:o.closeManagerActionSheet,closeOnClickOverlay:!0,safeAreaInsetBottom:!0},null,8,["show","actions","onSelect","onClose"])])),_:1})])),_:1})}],["__scopeId","data-v-a78351a8"]]);export{J as default};
