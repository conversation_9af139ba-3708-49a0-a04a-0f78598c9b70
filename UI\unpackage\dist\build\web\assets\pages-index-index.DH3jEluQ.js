import{_ as t,o as a,c as e,w as s,j as l,t as i,n,l as o,m as u,p as r,a as c,s as d,q as f,u as m,v as _,x as h,y as p,i as w,h as C,z as v,k as V}from"./index-Bl1z7-qC.js";import{T as b}from"./TimeFilter.DV_809cK.js";import{g}from"./dashboard.65zGmURY.js";import{p as y}from"./permission-mixin.QQrgJw9R.js";function D(t={}){return u.get("/Statistics/overview",t)}const x=t({mixins:[y],components:{CountUp:t({name:"CountUp",props:{endValue:{type:[Number,String],required:!0,default:0},suffix:{type:String,default:""},duration:{type:Number,default:1e3},decimals:{type:Number,default:0},customClass:{type:String,default:""}},data:()=>({displayValue:"0",startValue:0,interval:null}),watch:{endValue:{handler(t){null!=t&&this.startAnimation(t)},immediate:!0}},methods:{startAnimation(t){this.interval&&clearInterval(this.interval);const a=this.parseValue(t),e=this.parseValue(this.displayValue);if(e===a)return void(this.displayValue=this.formatNumber(a));const s=this.duration,l=Math.min(s/20,25),i=(a-e)/20;let n=0;this.interval=setInterval((()=>{if(n++,n>=20)this.displayValue=this.formatNumber(a),clearInterval(this.interval);else{const t=e+i*n;this.displayValue=this.formatNumber(t)}}),l)},parseValue(t){const a=parseFloat(t);return isNaN(a)?0:a},formatNumber(t){const a=Number(t).toFixed(this.decimals);return isNaN(a)?"0":a}},beforeDestroy(){this.interval&&clearInterval(this.interval)}},[["render",function(t,u,r,c,d,f){const m=o;return a(),e(m,{class:n(r.customClass)},{default:s((()=>[l(i(d.displayValue)+i(r.suffix),1)])),_:1},8,["class"])}],["__scopeId","data-v-068eabf7"]]),TimeFilter:b},data:()=>({statisticsData:{totalMembers:0,todayNewMembers:0,todayViewers:0,totalViewCount:0,totalCompleteViewCount:0,totalAnswerCount:0,totalCorrectAnswerCount:0,totalRewardCount:0,totalRewardAmountYuan:0,avgCompleteRate:0,avgCorrectRate:0},activeTimeFilter:"today",customDateRange:null,sectionState:{courses:!0,quizzes:!0,rewards:!0}}),onLoad(){const t=window.location.href;if(console.log("首页onLoad - 当前URL:",t),t.includes("/pages/video/index")){console.log("检测到视频页面访问，直接跳转");const t=window.location.hash;if(t&&t.length>1){const a=t.substring(1);return console.log("跳转到视频页面:",a),void r({url:a})}}this.canAccessPage("/pages/index/index")?(console.log("当前用户类型:",this.currentUserType),this.initDashboardData(),d()):c.redirectToLogin()},onShow(){d()},methods:{async initDashboardData(){try{f({title:"加载数据中..."}),await this.loadStatisticsData("today"),m()}catch(t){console.error("初始化数据失败:",t),m(),_({title:"数据加载失败",icon:"none",duration:2e3})}},async loadStatisticsData(t,a=null,e=null){try{let l={};if(a&&e)l={startDate:a,endDate:e};else if("today"===t){const t=new Date,a=t=>`${t.getFullYear()}-${(t.getMonth()+1).toString().padStart(2,"0")}-${t.getDate().toString().padStart(2,"0")}`;l.startDate=a(t),l.endDate=a(t)}let i=null,n=null;try{[i,n]=await Promise.all([D(l),g(l)])}catch(s){console.error("API调用失败:",s)}const o=i&&i.success,u=n&&n.success;if(o&&u){const t=i.data,a=n.data;this.statisticsData={totalMembers:a.totalMembers||0,todayNewMembers:a.todayNewMembers||0,todayViewers:a.todayViewers||0,totalViewCount:t.totalViewCount||0,totalCompleteViewCount:t.totalCompleteViewCount||0,totalAnswerCount:t.totalAnswerCount||0,totalCorrectAnswerCount:t.totalCorrectAnswerCount||0,totalRewardCount:t.totalRewardCount||0,totalRewardAmountYuan:t.totalRewardAmountYuan||0,avgCompleteRate:t.avgCompleteRate||0,avgCorrectRate:t.avgCorrectRate||0}}else console.warn("API数据获取失败，显示空数据:",{overviewSuccess:o,keyMetricsSuccess:u,overviewResponse:i?i.msg:"null",keyMetricsResponse:n?n.msg:"null"}),_({title:"数据加载失败",icon:"none",duration:2e3})}catch(l){throw console.error("加载统计数据失败:",l),l}},async selectTimeTab(t){try{f({title:"加载中..."}),await this.loadStatisticsData(t),m()}catch(a){console.error("切换时间标签失败:",a),m(),_({title:"数据加载失败",icon:"none",duration:2e3})}},async handleTimeFilterChange(t){try{this.activeTimeFilter=t,this.customDateRange={startDate:t.startDate,endDate:t.endDate},console.log("时间筛选变化:",t),f({title:"加载中..."}),await this.loadStatisticsData(t.type,t.startDate,t.endDate),m(),_({title:`已加载${t.startDate}至${t.endDate}的数据`,icon:"none",duration:2e3})}catch(a){console.error("时间筛选数据加载失败:",a),m(),_({title:"数据加载失败",icon:"none",duration:2e3})}}}},[["render",function(t,i,n,u,r,c){const d=h("CountUp"),f=w,m=o,_=h("TimeFilter"),b=p("permission");return a(),e(f,{class:"container"},{default:s((()=>[C(f,{class:"page-header"},{default:s((()=>[C(f,{class:"summary-section"},{default:s((()=>[C(f,{class:"stats-grid"},{default:s((()=>[C(f,{class:"stat-card"},{default:s((()=>[C(f,{class:"stat-value"},{default:s((()=>[C(d,{endValue:r.statisticsData.totalMembers,suffix:"人",duration:1e3,customClass:"stat-number"},null,8,["endValue"])])),_:1}),C(m,{class:"stat-label"},{default:s((()=>[l("会员总数")])),_:1})])),_:1}),C(f,{class:"stat-card"},{default:s((()=>[C(f,{class:"stat-value"},{default:s((()=>[C(d,{endValue:r.statisticsData.todayNewMembers,suffix:"人",duration:1e3,customClass:"stat-number"},null,8,["endValue"])])),_:1}),C(m,{class:"stat-label"},{default:s((()=>[l("今日新增")])),_:1})])),_:1}),C(f,{class:"stat-card"},{default:s((()=>[C(f,{class:"stat-value"},{default:s((()=>[C(d,{endValue:r.statisticsData.todayViewers,suffix:"人",duration:1e3,customClass:"stat-number"},null,8,["endValue"])])),_:1}),C(m,{class:"stat-label"},{default:s((()=>[l("今日观看")])),_:1})])),_:1})])),_:1})])),_:1}),C(f,{class:"time-filter-section"},{default:s((()=>[C(f,{class:"time-filter-title"},{default:s((()=>[C(m,null,{default:s((()=>[l("时间筛选")])),_:1}),C(m,{class:"time-filter-subtitle"},{default:s((()=>[l("选择查看不同时间段的数据")])),_:1})])),_:1}),C(_,{modelValue:r.activeTimeFilter,"onUpdate:modelValue":i[0]||(i[0]=t=>r.activeTimeFilter=t),onChange:c.handleTimeFilterChange},null,8,["modelValue","onChange"])])),_:1})])),_:1}),C(f,{class:"page-content"},{default:s((()=>[v((a(),e(f,{class:"data-section"},{default:s((()=>[C(f,{class:"section-header"},{default:s((()=>[C(f,{class:"section-title-wrapper"},{default:s((()=>[C(m,{class:"section-title"},{default:s((()=>[l("课程统计")])),_:1})])),_:1})])),_:1}),r.sectionState.courses?(a(),e(f,{key:0,class:"section-content"},{default:s((()=>[C(f,{class:"stats-grid"},{default:s((()=>[C(f,{class:"stat-card"},{default:s((()=>[C(m,{class:"stat-label"},{default:s((()=>[l("观看次数")])),_:1}),C(f,{class:"stat-value"},{default:s((()=>[C(d,{endValue:r.statisticsData.totalViewCount,suffix:"次",duration:1e3,customClass:"stat-number"},null,8,["endValue"])])),_:1})])),_:1}),C(f,{class:"stat-card"},{default:s((()=>[C(m,{class:"stat-label"},{default:s((()=>[l("完播次数")])),_:1}),C(f,{class:"stat-value"},{default:s((()=>[C(d,{endValue:r.statisticsData.totalCompleteViewCount,suffix:"次",duration:1e3,customClass:"stat-number"},null,8,["endValue"])])),_:1})])),_:1}),C(f,{class:"stat-card"},{default:s((()=>[C(m,{class:"stat-label"},{default:s((()=>[l("完播率")])),_:1}),C(f,{class:"stat-value"},{default:s((()=>[C(d,{endValue:r.statisticsData.avgCompleteRate,suffix:"%",duration:1e3,decimals:2,customClass:"stat-number"},null,8,["endValue"])])),_:1})])),_:1})])),_:1})])),_:1})):V("",!0)])),_:1})),[[b,"view_statistics","feature"]]),v((a(),e(f,{class:"data-section"},{default:s((()=>[C(f,{class:"section-header"},{default:s((()=>[C(f,{class:"section-title-wrapper"},{default:s((()=>[C(m,{class:"section-title"},{default:s((()=>[l("答题统计")])),_:1})])),_:1})])),_:1}),r.sectionState.quizzes?(a(),e(f,{key:0,class:"section-content"},{default:s((()=>[C(f,{class:"stats-grid"},{default:s((()=>[C(f,{class:"stat-card"},{default:s((()=>[C(m,{class:"stat-label"},{default:s((()=>[l("答题次数")])),_:1}),C(f,{class:"stat-value"},{default:s((()=>[C(d,{endValue:r.statisticsData.totalAnswerCount,suffix:"次",duration:1e3,customClass:"stat-number"},null,8,["endValue"])])),_:1})])),_:1}),C(f,{class:"stat-card"},{default:s((()=>[C(m,{class:"stat-label"},{default:s((()=>[l("正确次数")])),_:1}),C(f,{class:"stat-value"},{default:s((()=>[C(d,{endValue:r.statisticsData.totalCorrectAnswerCount,suffix:"次",duration:1e3,customClass:"stat-number"},null,8,["endValue"])])),_:1})])),_:1}),C(f,{class:"stat-card"},{default:s((()=>[C(m,{class:"stat-label"},{default:s((()=>[l("正确率")])),_:1}),C(f,{class:"stat-value"},{default:s((()=>[C(d,{endValue:r.statisticsData.avgCorrectRate,suffix:"%",duration:1e3,decimals:2,customClass:"stat-number"},null,8,["endValue"])])),_:1})])),_:1})])),_:1})])),_:1})):V("",!0)])),_:1})),[[b,"view_statistics","feature"]]),v((a(),e(f,{class:"data-section"},{default:s((()=>[C(f,{class:"section-header"},{default:s((()=>[C(f,{class:"section-title-wrapper"},{default:s((()=>[C(m,{class:"section-title"},{default:s((()=>[l("红包统计")])),_:1})])),_:1})])),_:1}),r.sectionState.rewards?(a(),e(f,{key:0,class:"section-content"},{default:s((()=>[C(f,{class:"stats-grid grid-2"},{default:s((()=>[C(f,{class:"stat-card"},{default:s((()=>[C(m,{class:"stat-label"},{default:s((()=>[l("红包数量")])),_:1}),C(f,{class:"stat-value"},{default:s((()=>[C(d,{endValue:r.statisticsData.totalRewardCount,suffix:"个",duration:1e3,customClass:"stat-number"},null,8,["endValue"])])),_:1})])),_:1}),C(f,{class:"stat-card"},{default:s((()=>[C(m,{class:"stat-label"},{default:s((()=>[l("红包金额")])),_:1}),C(f,{class:"stat-value"},{default:s((()=>[C(d,{endValue:r.statisticsData.totalRewardAmountYuan,suffix:"元",duration:1e3,decimals:2,customClass:"stat-number"},null,8,["endValue"])])),_:1})])),_:1})])),_:1})])),_:1})):V("",!0)])),_:1})),[[b,"view_statistics","feature"]])])),_:1})])),_:1})}],["__scopeId","data-v-c3060941"]]);export{x as default};
