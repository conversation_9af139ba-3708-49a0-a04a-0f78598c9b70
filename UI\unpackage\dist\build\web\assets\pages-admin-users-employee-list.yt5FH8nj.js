import{_ as e,a0 as a,c as s,w as l,i as o,o as n,h as t,j as d,l as r}from"./index-Bl1z7-qC.js";const m=e({name:"EmployeeListRedirect",onLoad(e){console.log("employee-list.vue 重定向到统一页面，参数:",e);let s="/pages/admin/users/user-management?type=employee";e.managerId&&(s+=`&managerId=${e.managerId}`),console.log("重定向URL:",s),a({url:s})}},[["render",function(e,a,m,c,u,i){const g=r,p=o;return n(),s(p,{class:"redirect-page"},{default:l((()=>[t(p,{class:"loading"},{default:l((()=>[t(g,null,{default:l((()=>[d("正在跳转到员工页面...")])),_:1})])),_:1})])),_:1})}],["__scopeId","data-v-f99dfb51"]]);export{m as default};
