import{_ as e,K as t,S as i,v as s,aD as a,a as o,M as l,aE as n,q as d,R as c,u as r,A as h,h as u,w as m,c as f,k as b,F as v,i as g,o as p,j as _,t as I,P as w,l as y,C as D}from"./index-Bl1z7-qC.js";import{g as k}from"./batch.Bdv1419a.js";import{m as V}from"./media-common.7odKdPQQ.js";import{p as C}from"./permission-mixin.QQrgJw9R.js";import{a as U,b as A}from"./admin-helpers.CPO-gyAZ.js";const x=e({mixins:[V,C],data:()=>({batchId:null,batch:null,batchVideos:[],showActionMenu:!1,showDeleteDialog:!1}),onLoad(e){const{id:t}=e??{};t?(this.batchId=t,this.fetchBatchDetails()):U("批次ID缺失")},methods:{async fetchBatchDetails(){const e=await A((()=>k(this.batchId)),"加载批次详情...","加载批次详情失败");if(!e.success||!e.data)throw new Error(e.msg||"获取批次详情失败");{const t=e.data;this.batch={id:t.id,batchId:`B${t.id}`,title:t.name??t.title,status:this.mapBatchStatus(t.status),createTime:this.formatDate(t.createTime),startTime:this.formatDate(t.startTime),endTime:this.formatDate(t.endTime),creator:t.creatorName??"未知",videoCount:1,totalViews:t.currentParticipants??0,participants:t.currentParticipants??0,totalReward:t.rewardAmount??0,redPacketAmount:t.redPacketAmount??0,description:t.description??"",videoId:t.videoId,videoTitle:t.videoTitle,videoDescription:t.videoDescription,videoCoverUrl:t.videoCoverUrl,videoUrl:t.videoUrl,videoDuration:t.videoDuration,questions:t.questions||[],statistics:t.statistics||{}},console.log("设置的批次信息:",this.batch),console.log("批次标题:",this.batch.title),console.log("批次描述:",this.batch.description),this.loadBatchVideos()}},mapBatchStatus:e=>({0:"pending",1:"active",2:"ended",3:"paused"}[e]||"pending"),loadBatchVideos(){this.batch.videoId?(this.batchVideos=[{id:this.batch.videoId,title:this.batch.videoTitle||this.batch.title||"该批次无视频",description:this.batch.videoDescription||"",videoUrl:this.buildCompleteFileUrl(this.batch.videoUrl),thumbnail:this.buildCompleteFileUrl(this.batch.videoCoverUrl)||"/assets/images/video-cover.jpg",duration:this.batch.videoDuration||0,views:this.batch.totalViews||0,likes:0,comments:0}],console.log("构造的视频数据:",this.batchVideos[0])):(this.batchVideos=[],console.log("批次中没有视频数据"))},goBack(){t()},viewRealTimeData(){i({url:`/pages/admin/media/batch-realtime?id=${this.batchId}`})},showAddVideoModal(){s({title:"添加视频功能开发中",icon:"none"})},viewVideoDetail(e){i({url:`/pages/admin/media/detail?id=${e.id}`})},showVideoMenu(e){a({itemList:["移出批次","查看详情"],success:t=>{0===t.tapIndex?s({title:"移出批次功能开发中",icon:"none"}):1===t.tapIndex&&this.viewVideoDetail(e)}})},getBatchStatusClass:e=>"ended"===e.status?"status-expired":"pending"===e.status?"status-scheduled":"status-active",getBatchStatusText:e=>"ended"===e.status?"已结束":"pending"===e.status?"未开始":"进行中",handleVideoError(e){var t;let i="视频加载失败";(null==(t=e.detail)?void 0:t.errMsg)&&(i+=`: ${e.detail.errMsg}`),this.batchVideos.length>0&&(this.batchVideos[0].videoUrl||(this.batchVideos[0].videoUrl="/static/videos/sample.mp4",i="原视频缺失，已替换为示例视频")),s({title:i,icon:"none",duration:3e3})},getVideoShareUrl(){try{let t=null;try{t=o.getLoginInfo(),console.log("adminAuthService.getLoginInfo():",t)}catch(e){console.log("adminAuthService 不可用，从存储获取:",e),t=l("adminLoginInfo"),console.log("uni.getStorageSync(adminLoginInfo):",t)}if(console.log("最终获取的 loginInfo:",t),console.log("loginInfo.userId:",null==t?void 0:t.userId),console.log("loginInfo.username:",null==t?void 0:t.username),console.log("loginInfo.id:",null==t?void 0:t.id),console.log("loginInfo.employeeId:",null==t?void 0:t.employeeId),!t)return console.error("管理员信息缺失:",{loginInfo:t}),"获取链接失败：管理员信息缺失";const i=t.userId||t.username||t.id||t.employeeId||"admin";if(console.log("使用的 sharerId:",i),!this.batch||!this.batch.videoId||!this.batchId)return"获取链接失败：批次信息不完整";const s=n();return console.log("当前配置的UIProjectUrl:",s),console.log("window.APP_CONFIG:",window.APP_CONFIG),`${s}/#/pages/video/index?videoId=${this.batch.videoId}&batchId=${this.batchId}&sharerId=${i}`}catch(e){return console.error("获取分享链接失败:",e),this.$toast(e.message||"获取链接失败"),`配置错误：${e.message}`}},copyVideoLink(){try{const e=this.getVideoShareUrl();if(e.includes("获取链接失败"))return void s({title:e,icon:"none",duration:3e3});d({title:"复制中..."}),c({data:e,success:()=>{r(),s({title:"分享链接已复制到剪贴板",icon:"success",duration:2e3}),console.log("分享链接已复制:",e)},fail:e=>{r(),console.error("复制到剪贴板失败:",e),s({title:"复制失败，请手动复制链接",icon:"none",duration:3e3})}})}catch(e){r(),console.error("复制链接失败:",e),s({title:"复制失败，请重试",icon:"none",duration:3e3})}},viewBatchData(){i({url:`/pages/admin/media/batch-data?id=${this.batchId}`})},showActionMenuHandler(){this.showActionMenu=!0},hideActionMenu(){this.showActionMenu=!1},getQuizCount(){return this.batch.questions&&Array.isArray(this.batch.questions)?this.batch.questions.length:0},showDeleteConfirm(){this.hideActionMenu(),this.showDeleteDialog=!0},hideDeleteConfirm(){this.showDeleteDialog=!1},deleteBatch(){this.hideDeleteConfirm(),s({title:"批次已删除",icon:"success"}),setTimeout((()=>{t()}),1500)}}},[["render",function(e,t,i,s,a,o){const l=w,n=y,d=g,c=D;return p(),h(v,null,[u(d,{class:"page-container"},{default:m((()=>[u(d,{class:"main-layout"},{default:m((()=>[u(d,{class:"content-main"},{default:m((()=>[a.batchVideos.length>0?(p(),f(d,{key:0,class:"video-card"},{default:m((()=>[u(d,{class:"video-player-container"},{default:m((()=>[u(l,{src:a.batchVideos[0].videoUrl||"/assets/videos/sample.mp4",poster:a.batchVideos[0].thumbnail,controls:"",class:"video-player",onError:o.handleVideoError},null,8,["src","poster","onError"]),a.batchVideos[0].videoUrl?b("",!0):(p(),f(d,{key:0,class:"video-overlay"},{default:m((()=>[u(n,{class:"overlay-text"},{default:m((()=>[_("视频加载中...")])),_:1})])),_:1}))])),_:1}),u(d,{class:"video-info"},{default:m((()=>[u(d,{class:"video-header"},{default:m((()=>[u(n,{class:"video-title"},{default:m((()=>[_(I(a.batch.title),1)])),_:1})])),_:1}),a.batch.description?(p(),f(d,{key:0,class:"video-description"},{default:m((()=>[u(n,{class:"description-icon"},{default:m((()=>[_("ℹ️")])),_:1}),u(n,{class:"description-text"},{default:m((()=>[_(I(a.batch.description),1)])),_:1})])),_:1})):b("",!0),u(d,{class:"link-info-section"},{default:m((()=>[u(d,{class:"link-info-header"},{default:m((()=>[u(n,{class:"link-icon"},{default:m((()=>[_("�")])),_:1}),u(n,{class:"link-title"},{default:m((()=>[_("分享链接")])),_:1})])),_:1}),u(d,{class:"link-url-display"},{default:m((()=>[u(n,{class:"link-url"},{default:m((()=>[_(I(o.getVideoShareUrl()),1)])),_:1})])),_:1})])),_:1}),u(d,{class:"action-buttons"},{default:m((()=>[u(d,{class:"button-row"},{default:m((()=>[u(c,{class:"action-btn copy-link-btn",onClick:o.copyVideoLink},{default:m((()=>[u(n,{class:"btn-text"},{default:m((()=>[_("复制链接")])),_:1})])),_:1},8,["onClick"]),u(c,{class:"action-btn view-data-btn",onClick:o.viewBatchData},{default:m((()=>[u(n,{class:"btn-text"},{default:m((()=>[_("查看数据")])),_:1})])),_:1},8,["onClick"])])),_:1}),e.canUseFeature("delete_batch")?(p(),f(d,{key:0,class:"button-row full-width"},{default:m((()=>[u(c,{class:"delete-btn danger",onClick:o.showDeleteConfirm},{default:m((()=>[u(n,{class:"btn-text"},{default:m((()=>[_("删除批次")])),_:1})])),_:1},8,["onClick"])])),_:1})):b("",!0)])),_:1})])),_:1})])),_:1})):b("",!0)])),_:1})])),_:1}),0===a.batchVideos.length?(p(),f(d,{key:0,class:"empty-state"},{default:m((()=>[u(d,{class:"empty-icon"},{default:m((()=>[u(n,{class:"iconfont icon-video-off"})])),_:1}),u(n,{class:"empty-title"},{default:m((()=>[_("该批次暂无视频")])),_:1}),u(n,{class:"empty-desc"},{default:m((()=>[_("请添加视频内容或选择其他批次")])),_:1})])),_:1})):b("",!0)])),_:1}),a.showDeleteDialog?(p(),f(d,{key:0,class:"modal-overlay"},{default:m((()=>[u(d,{class:"confirm-modal"},{default:m((()=>[u(d,{class:"modal-header"},{default:m((()=>[u(n,{class:"modal-title"},{default:m((()=>[_("确认删除")])),_:1})])),_:1}),u(d,{class:"modal-body"},{default:m((()=>[u(n,{class:"confirm-text"},{default:m((()=>[_('确定要删除批次"'+I(a.batch.title)+'"吗？删除后无法恢复，请谨慎操作。',1)])),_:1})])),_:1}),u(d,{class:"modal-footer"},{default:m((()=>[u(c,{class:"btn secondary",onClick:o.hideDeleteConfirm},{default:m((()=>[_("取消")])),_:1},8,["onClick"]),u(c,{class:"btn danger",onClick:o.deleteBatch},{default:m((()=>[_("确认删除")])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1})):b("",!0)],64)}],["__scopeId","data-v-9255a322"]]);export{x as default};
