import{_ as s,a as e,r as a,b as o,c as r,w as t,n as i,i as n,d as l,e as d,f as u,g as c,o as m,h as g,j as h,t as p,k as w,l as f}from"./index-Bl1z7-qC.js";const L=s({data:()=>({loginForm:{username:"",password:""},showPassword:!1,isLoading:!1,pageLoaded:!1,errors:{username:"",password:""}}),computed:{canSubmit(){return this.loginForm.username.trim()&&this.loginForm.password.trim()&&!this.isLoading}},onLoad(){this.checkExistingLogin(),setTimeout((()=>{this.pageLoaded=!0}),100)},methods:{resetForm(){this.loginForm={username:"",password:""},this.errors={username:"",password:""},this.showPassword=!1,this.isLoading=!1},togglePassword(){this.showPassword=!this.showPassword},validateUsername(){const s=this.loginForm.username.trim();return s?s.length<3?(this.errors.username="用户名至少3个字符",!1):/^[a-zA-Z0-9_]+$/.test(s)?(this.errors.username="",!0):(this.errors.username="用户名只能包含字母、数字和下划线",!1):(this.errors.username="请输入用户名",!1)},validatePassword(){const s=this.loginForm.password.trim();return s?s.length<6?(this.errors.password="密码至少6个字符",!1):(this.errors.password="",!0):(this.errors.password="请输入密码",!1)},clearError(s){this.errors[s]&&(this.errors[s]="")},validateForm(){const s=this.validateUsername(),e=this.validatePassword();return s&&e},async handleLogin(){if(this.validateForm()){if(this.canSubmit&&!this.isLoading){this.isLoading=!0;try{const{username:s,password:a}=this.loginForm,o=await e.authenticate(s,a);o.success?(this.showToastMessage(o.message,"success"),await this.delay(1e3),e.redirectToMain()):(this.showToastMessage(o.message,"error"),this.loginForm.password="")}catch(s){console.error("Login error:",s),this.showToastMessage("登录失败，请重试","error"),this.loginForm.password=""}finally{this.isLoading=!1}}}else this.showToastMessage("请检查输入信息","error")},checkExistingLogin(){e.isLoggedIn()&&e.isSessionValid()?e.redirectToMain():e.isLoggedIn()&&e.logout()},showToastMessage(s,e="success"){this.$refs.uToast.show({message:s,type:e,duration:3e3})},delay:s=>new Promise((e=>setTimeout(e,s))),quickLogin(s,e){this.isLoading||(this.loginForm.username=s,this.loginForm.password=e,this.errors.username="",this.errors.password="",this.handleLogin())}}},[["render",function(s,e,L,_,b,F){const k=f,y=n,P=a(o("u-input"),l),T=a(o("u-icon"),d),v=a(o("u-button"),u),x=a(o("u-toast"),c);return m(),r(y,{class:i(["login-container",{"page-loaded":b.pageLoaded}])},{default:t((()=>[g(y,{class:"login-content"},{default:t((()=>[g(y,{class:i(["header-section",{"animate-in":b.pageLoaded}])},{default:t((()=>[g(k,{class:"app-title"},{default:t((()=>[h("视频答题系统")])),_:1}),g(y,{class:"title-decoration"})])),_:1},8,["class"]),g(y,{class:i(["form-container",{"animate-in":b.pageLoaded}])},{default:t((()=>[g(y,{class:i(["input-group",{"animate-in":b.pageLoaded}])},{default:t((()=>[g(P,{modelValue:b.loginForm.username,"onUpdate:modelValue":e[0]||(e[0]=s=>b.loginForm.username=s),placeholder:"请输入用户名",border:"surround",clearable:"",error:!!b.errors.username,onBlur:F.validateUsername,onInput:e[1]||(e[1]=s=>F.clearError("username")),class:"login-input"},null,8,["modelValue","error","onBlur"]),b.errors.username?(m(),r(k,{key:0,class:"error-message animate-shake"},{default:t((()=>[h(p(b.errors.username),1)])),_:1})):w("",!0)])),_:1},8,["class"]),g(y,{class:i(["input-group",{"animate-in":b.pageLoaded}])},{default:t((()=>[g(P,{modelValue:b.loginForm.password,"onUpdate:modelValue":e[2]||(e[2]=s=>b.loginForm.password=s),placeholder:"请输入密码",border:"surround",clearable:"",password:!b.showPassword,error:!!b.errors.password,onBlur:F.validatePassword,onInput:e[3]||(e[3]=s=>F.clearError("password")),class:"login-input"},{suffix:t((()=>[g(T,{name:b.showPassword?"eye":"eye-off",size:"20",color:"#186BFF",onClick:F.togglePassword,class:"password-toggle"},null,8,["name","onClick"])])),_:1},8,["modelValue","password","error","onBlur"]),b.errors.password?(m(),r(k,{key:0,class:"error-message animate-shake"},{default:t((()=>[h(p(b.errors.password),1)])),_:1})):w("",!0)])),_:1},8,["class"]),g(v,{type:"primary",text:b.isLoading?"登录中...":"登录",loading:b.isLoading,disabled:!F.canSubmit||b.isLoading,onClick:F.handleLogin,class:i(["login-btn",{"animate-in":b.pageLoaded,"btn-loading":b.isLoading}])},null,8,["text","loading","disabled","onClick","class"]),g(y,{class:"test-accounts"},{default:t((()=>[g(k,{class:"test-title"},{default:t((()=>[h("测试账号")])),_:1}),g(y,{class:"test-grid"},{default:t((()=>[g(v,{type:"info",size:"small",disabled:b.isLoading,onClick:e[4]||(e[4]=s=>F.quickLogin("super_admin","123456")),class:"test-btn test-btn-first"},{default:t((()=>[g(y,{class:"test-btn-content"},{default:t((()=>[g(k,{class:"test-username"},{default:t((()=>[h("super_admin")])),_:1}),g(k,{class:"test-role"},{default:t((()=>[h("超管")])),_:1})])),_:1})])),_:1},8,["disabled"]),g(v,{type:"info",size:"small",disabled:b.isLoading,onClick:e[5]||(e[5]=s=>F.quickLogin("admin","123456")),class:"test-btn test-btn-middle"},{default:t((()=>[g(y,{class:"test-btn-content"},{default:t((()=>[g(k,{class:"test-username"},{default:t((()=>[h("admin")])),_:1}),g(k,{class:"test-role"},{default:t((()=>[h("管理")])),_:1})])),_:1})])),_:1},8,["disabled"]),g(v,{type:"info",size:"small",disabled:b.isLoading,onClick:e[6]||(e[6]=s=>F.quickLogin("emp1","123456")),class:"test-btn test-btn-last"},{default:t((()=>[g(y,{class:"test-btn-content"},{default:t((()=>[g(k,{class:"test-username"},{default:t((()=>[h("emp1")])),_:1}),g(k,{class:"test-role"},{default:t((()=>[h("员工1")])),_:1})])),_:1})])),_:1},8,["disabled"])])),_:1})])),_:1})])),_:1},8,["class"]),g(y,{class:"footer"},{default:t((()=>[g(k,{class:"footer-text"},{default:t((()=>[h("© 2024 视频答题系统")])),_:1}),g(k,{class:"footer-version"},{default:t((()=>[h("Version 1.0.0")])),_:1})])),_:1})])),_:1}),g(x,{ref:"uToast"},null,512)])),_:1},8,["class"])}],["__scopeId","data-v-35d31ce6"]]);export{L as default};
