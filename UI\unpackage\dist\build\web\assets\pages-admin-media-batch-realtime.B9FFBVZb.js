import{_ as a,v as t,S as s,K as e,c as l,w as d,i as c,o as i,h as r,j as u,t as o,n,A as f,B as _,F as h,l as m,C as p,ak as b,al as w}from"./index-Bl1z7-qC.js";import{g}from"./batch.Bdv1419a.js";import{a as v}from"./dashboard.65zGmURY.js";import{a as y,b as T}from"./admin-helpers.CPO-gyAZ.js";const k=a({data:()=>({batchId:"",batch:{id:"",batchId:"",title:"",status:"",startTime:"",endTime:"",totalViews:0,participants:0,totalReward:0},realTimeData:{currentOnline:0,todayViews:0,todayParticipants:0,todayCompletions:0,completionRate:0},recentViewsData:[],topVideos:[],topUsers:[]}),onLoad(a){const{id:t}=a??{};t?(this.batchId=t,this.loadBatchDetail(),this.loadRealTimeData(),this.loadTopVideos(),this.loadTopUsers()):y("批次ID缺失")},methods:{async loadBatchDetail(){const a=await T((()=>g(this.batchId)),"加载批次信息...","加载批次信息失败");if(!a.success||!a.data)throw new Error(a.msg||"获取批次详情失败");{const t=a.data;this.batch={id:t.id,batchId:`B${t.id}`,title:t.name??t.title,status:this.mapBatchStatus(t.status),createTime:t.createTime,startTime:t.startTime,endTime:t.endTime,creator:t.creatorName??"未知",videoCount:1,totalViews:t.currentParticipants??0,participants:t.currentParticipants??0,totalReward:t.rewardAmount??0,redPacketAmount:t.redPacketAmount??0}}},mapBatchStatus:a=>({0:"pending",1:"active",2:"ended",3:"paused"}[a]||"pending"),async loadRealTimeData(){const a={batchId:this.batchId,timeRange:"today"},t=await T((()=>v(a)),"加载实时数据...","加载实时数据失败");if(!t.success||!t.data)throw new Error(t.msg||"获取实时数据失败");{const a=t.data;this.realTimeData={currentOnline:a.currentOnline??0,todayViews:a.todayViews??0,todayParticipants:a.todayParticipants??0,todayCompletions:a.todayCompletions??0,completionRate:a.completionRate??0}}},loadTopVideos(){this.topVideos=[]},loadTopUsers(){this.topUsers=[]},async refreshData(){await T((async()=>{await Promise.all([this.loadRealTimeData(),this.loadTopVideos(),this.loadTopUsers()])}),"刷新数据中...","刷新失败"),t({title:"数据已更新",icon:"success"})},getBatchStatusClass:a=>"ended"===a.status?"status-expired":"pending"===a.status?"status-scheduled":"status-active",getBatchStatusText:a=>"ended"===a.status?"已过期":"pending"===a.status?"未开始":"进行中",formatDate(a){if(!a)return"未设置";const t=new Date(a);return`${t.getFullYear()}-${(t.getMonth()+1).toString().padStart(2,"0")}-${t.getDate().toString().padStart(2,"0")}`},viewVideoDetail(a){s({url:`/pages/admin/media/detail?id=${a.id}`})},goBack(){e()}}},[["render",function(a,t,s,e,g,v){const y=m,T=c,k=p,D=w;return i(),l(T,{class:"container"},{default:d((()=>[r(T,{class:"page-header"},{default:d((()=>[r(T,{class:"header-section"},{default:d((()=>[r(T,{class:"title-area"},{default:d((()=>[r(T,{class:"back-btn",onClick:v.goBack},{default:d((()=>[r(y,{class:"iconfont icon-back"})])),_:1},8,["onClick"]),r(T,null,{default:d((()=>[r(y,{class:"page-title"},{default:d((()=>[u(o(g.batch.title),1)])),_:1}),r(T,{class:"page-subtitle"},{default:d((()=>[u("实时数据")])),_:1})])),_:1})])),_:1}),r(T,{class:"action-area"},{default:d((()=>[r(k,{class:"refresh-btn",onClick:v.refreshData},{default:d((()=>[r(y,{class:"refresh-icon"},{default:d((()=>[u("🔄")])),_:1}),r(y,null,{default:d((()=>[u("刷新")])),_:1})])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1}),r(T,{class:"page-header-1"}),r(T,{class:"batch-info-card"},{default:d((()=>[r(T,{class:"batch-title-row"},{default:d((()=>[r(y,{class:"batch-title"},{default:d((()=>[u(o(g.batch.title),1)])),_:1}),r(T,{class:n(["batch-status",v.getBatchStatusClass(g.batch)])},{default:d((()=>[u(o(v.getBatchStatusText(g.batch)),1)])),_:1},8,["class"])])),_:1}),r(T,{class:"batch-id"},{default:d((()=>[u("批次编号: "+o(g.batch.batchId),1)])),_:1}),r(T,{class:"batch-period"},{default:d((()=>[u(o(v.formatDate(g.batch.startTime))+" ~ "+o(v.formatDate(g.batch.endTime)),1)])),_:1})])),_:1}),r(T,{class:"data-overview"},{default:d((()=>[r(T,{class:"data-card"},{default:d((()=>[r(T,{class:"data-value"},{default:d((()=>[u(o(g.realTimeData.currentOnline),1)])),_:1}),r(T,{class:"data-label"},{default:d((()=>[u("当前在线")])),_:1})])),_:1}),r(T,{class:"data-card"},{default:d((()=>[r(T,{class:"data-value"},{default:d((()=>[u(o(g.realTimeData.todayViews),1)])),_:1}),r(T,{class:"data-label"},{default:d((()=>[u("今日观看")])),_:1})])),_:1}),r(T,{class:"data-card"},{default:d((()=>[r(T,{class:"data-value"},{default:d((()=>[u(o(g.realTimeData.todayParticipants),1)])),_:1}),r(T,{class:"data-label"},{default:d((()=>[u("今日参与人")])),_:1})])),_:1}),r(T,{class:"data-card"},{default:d((()=>[r(T,{class:"data-value"},{default:d((()=>[u(o(g.realTimeData.todayCompletions),1)])),_:1}),r(T,{class:"data-label"},{default:d((()=>[u("今日完成")])),_:1})])),_:1})])),_:1}),r(T,{class:"section-card"},{default:d((()=>[r(T,{class:"section-header"},{default:d((()=>[r(y,{class:"section-title"},{default:d((()=>[u("总体数据")])),_:1}),r(y,{class:"section-subtitle"},{default:d((()=>[u("累计统计")])),_:1})])),_:1}),r(T,{class:"total-data"},{default:d((()=>[r(T,{class:"data-row"},{default:d((()=>[r(T,{class:"data-item"},{default:d((()=>[r(y,{class:"data-label"},{default:d((()=>[u("总观看量")])),_:1}),r(y,{class:"data-value primary"},{default:d((()=>[u(o(g.batch.totalViews||0),1)])),_:1})])),_:1}),r(T,{class:"data-item"},{default:d((()=>[r(y,{class:"data-label"},{default:d((()=>[u("总参与人数")])),_:1}),r(y,{class:"data-value success"},{default:d((()=>[u(o(g.batch.participants||0),1)])),_:1})])),_:1})])),_:1}),r(T,{class:"data-row"},{default:d((()=>[r(T,{class:"data-item"},{default:d((()=>[r(y,{class:"data-label"},{default:d((()=>[u("完成率")])),_:1}),r(y,{class:"data-value warning"},{default:d((()=>[u(o(g.realTimeData.completionRate)+"%",1)])),_:1})])),_:1}),r(T,{class:"data-item"},{default:d((()=>[r(y,{class:"data-label"},{default:d((()=>[u("总奖励金额")])),_:1}),r(y,{class:"data-value danger"},{default:d((()=>[u(o(g.batch.totalReward||0)+"元",1)])),_:1})])),_:1})])),_:1})])),_:1}),r(T,{class:"data-charts"},{default:d((()=>[r(T,{class:"chart-title"},{default:d((()=>[u("近7天观看趋势")])),_:1}),r(T,{class:"chart-placeholder"},{default:d((()=>[r(T,{class:"chart-bars"},{default:d((()=>[(i(!0),f(h,null,_(g.recentViewsData,((a,t)=>(i(),l(T,{class:"chart-bar",key:t},{default:d((()=>[r(T,{class:"bar-value",style:b({height:a.height})},null,8,["style"]),r(T,{class:"bar-label"},{default:d((()=>[u(o(a.day),1)])),_:2},1024)])),_:2},1024)))),128))])),_:1})])),_:1})])),_:1})])),_:1}),r(T,{class:"section-card"},{default:d((()=>[r(T,{class:"section-header"},{default:d((()=>[r(y,{class:"section-title"},{default:d((()=>[u("热门视频排行")])),_:1}),r(y,{class:"section-subtitle"},{default:d((()=>[u("今日最受欢迎")])),_:1})])),_:1}),r(T,{class:"video-rank-list"},{default:d((()=>[(i(!0),f(h,null,_(g.topVideos,((a,t)=>(i(),l(T,{class:"video-rank-item",key:a.id,onClick:t=>v.viewVideoDetail(a)},{default:d((()=>[r(T,{class:n(["rank-number",t<3?"top-rank":""])},{default:d((()=>[u(o(t+1),1)])),_:2},1032,["class"]),r(T,{class:"video-thumbnail"},{default:d((()=>[r(D,{src:a.thumbnail,mode:"aspectFill"},null,8,["src"])])),_:2},1024),r(T,{class:"video-info"},{default:d((()=>[r(y,{class:"video-title"},{default:d((()=>[u(o(a.title),1)])),_:2},1024),r(T,{class:"video-stats"},{default:d((()=>[r(T,{class:"stat-item"},{default:d((()=>[r(y,{class:"stat-label"},{default:d((()=>[u("今日观看:")])),_:1}),r(y,{class:"stat-value"},{default:d((()=>[u(o(a.todayViews),1)])),_:2},1024)])),_:2},1024),r(T,{class:"stat-item"},{default:d((()=>[r(y,{class:"stat-label"},{default:d((()=>[u("当前在线:")])),_:1}),r(y,{class:"stat-value"},{default:d((()=>[u(o(a.currentOnline),1)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1032,["onClick"])))),128))])),_:1})])),_:1}),r(T,{class:"section-card"},{default:d((()=>[r(T,{class:"section-header"},{default:d((()=>[r(y,{class:"section-title"},{default:d((()=>[u("活跃用户排行")])),_:1}),r(y,{class:"section-subtitle"},{default:d((()=>[u("最积极用户的活跃")])),_:1})])),_:1}),r(T,{class:"user-rank-list"},{default:d((()=>[(i(!0),f(h,null,_(g.topUsers,((a,t)=>(i(),l(T,{class:"user-rank-item",key:a.id},{default:d((()=>[r(T,{class:n(["rank-number",t<3?"top-rank":""])},{default:d((()=>[u(o(t+1),1)])),_:2},1032,["class"]),r(T,{class:"user-avatar"},{default:d((()=>[r(D,{src:a.avatar,mode:"aspectFill"},null,8,["src"])])),_:2},1024),r(T,{class:"user-info"},{default:d((()=>[r(y,{class:"user-name"},{default:d((()=>[u(o(a.name),1)])),_:2},1024),r(y,{class:"user-type"},{default:d((()=>[u(o(a.type),1)])),_:2},1024)])),_:2},1024),r(T,{class:"user-progress"},{default:d((()=>[r(T,{class:"progress-bar"},{default:d((()=>[r(T,{class:"progress-inner",style:b({width:a.completionRate+"%"})},null,8,["style"])])),_:2},1024),r(y,{class:"progress-text"},{default:d((()=>[u("完成率 "+o(a.completionRate)+"%",1)])),_:2},1024)])),_:2},1024),r(T,{class:"user-reward"},{default:d((()=>[r(y,{class:"reward-value"},{default:d((()=>[u(o(a.totalReward)+"元",1)])),_:2},1024)])),_:2},1024)])),_:2},1024)))),128))])),_:1})])),_:1})])),_:1})}],["__scopeId","data-v-910c01cf"]]);export{k as default};
