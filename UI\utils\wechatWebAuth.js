/**
 * 微信网页授权工具类
 * 处理H5端的微信网页授权登录流程
 */

import { getAppConfig } from './config-manager.js'
import { getWechatAuthorizeUrl, buildWechatState, parseWechatState } from '../api/wechat.js'

// 日志回调函数，用于将日志输出到页面
let logCallback = null

/**
 * 设置日志回调函数
 * @param {Function} callback - 日志回调函数
 */
export function setLogCallback(callback) {
  logCallback = callback
}

/**
 * 内部日志函数
 * @param {string} message - 日志消息
 * @param {string} type - 日志类型
 */
function log(message, type = 'info') {
  console.log(`[WechatWebAuth] [${type.toUpperCase()}] ${message}`)
  if (logCallback) {
    logCallback(`[WechatWebAuth] ${message}`, type)
  }
}

/**
 * 微信网页授权工具类
 */
class WechatWebAuth {
  constructor() {
    this.config = null
    this.initConfig()
  }

  /**
   * 初始化配置
   */
  initConfig() {
    log('初始化微信配置...')

    // 从外部配置文件获取微信配置
    if (typeof window !== 'undefined' && window.APP_CONFIG) {
      log('从 window.APP_CONFIG 获取微信配置')
      this.config = window.APP_CONFIG.wechat
      log(`获取到的配置: ${JSON.stringify(this.config)}`)
    } else {
      log('使用备用微信配置')
      // 备用配置
      this.config = {
        appId: 'wx02bacea38f7f3ab5',
        scope: 'snsapi_userinfo',
        authBaseUrl: 'https://open.weixin.qq.com/connect/oauth2/authorize',
        redirectUri: {
          development: 'http://localhost:5173/pages/wechat-callback/index',
          production: 'https://your-domain.com/pages/wechat-callback/index'
        }
      }
      log(`备用配置: ${JSON.stringify(this.config)}`)
    }

    log('微信配置初始化完成')
  }

  /**
   * 检测当前环境是否为H5
   * @returns {boolean}
   */
  isH5Environment() {
    // #ifdef H5
    log('环境检测: H5环境')
    return true
    // #endif

    // #ifndef H5
    log('环境检测: 非H5环境')
    return false
    // #endif
  }

  /**
   * 检测是否在微信浏览器中
   * @returns {boolean}
   */
  isWechatBrowser() {
    if (typeof window === 'undefined') return false
    
    const ua = window.navigator.userAgent.toLowerCase()
    return ua.includes('micromessenger')
  }

  /**
   * 获取当前环境的回调地址
   * @returns {string}
   */
  getRedirectUri() {
    const isDev = process.env.NODE_ENV === 'development' || 
                  (typeof window !== 'undefined' && window.APP_CONFIG?.debugMode)
    
    return isDev ? 
      this.config.redirectUri.development : 
      this.config.redirectUri.production
  }

  /**
   * 生成随机状态参数
   * @param {Object} extraData - 额外需要传递的数据
   * @returns {string}
   */
  generateState(extraData = {}) {
    return buildWechatState(extraData)
  }

  /**
   * 解析状态参数
   * @param {string} state - 状态参数
   * @returns {Object|null}
   */
  parseState(state) {
    return parseWechatState(state)
  }

  /**
   * 构造微信授权URL（通过后端接口）
   * @param {Object} options - 配置选项
   * @param {string} options.state - 状态参数
   * @param {string} options.redirectUri - 自定义回调地址
   * @param {string} options.scope - 授权范围
   * @param {string} options.inviterId - 邀请人ID
   * @returns {Promise<string>} 授权URL
   */
  async buildAuthUrl(options = {}) {
    try {
      const {
        state = this.generateState(),
        redirectUri = this.getRedirectUri(),
        scope = this.config.scope || 'snsapi_userinfo'
      } = options

      const params = {
        redirectUri: redirectUri,
        state: state,
        scope: scope,
        inviterId: options.inviterId || null
      }

      console.log('调用授权URL接口，参数:', params)

      const response = await getWechatAuthorizeUrl(params)

      console.log('授权URL接口响应:', response)

      if (response.success && response.data) {
        // 确保返回的是字符串
        const authUrl = typeof response.data === 'string' ? response.data : response.data.url || response.data.authUrl

        if (typeof authUrl === 'string') {
          return authUrl
        } else {
          console.error('授权URL格式错误:', response.data)
          throw new Error('授权URL格式错误')
        }
      } else {
        throw new Error(response.msg || response.message || '获取授权URL失败')
      }
    } catch (error) {
      console.error('构造授权URL失败:', error)
      throw error
    }
  }

  /**
   * 直接构造微信授权URL（临时调试用）
   * @param {Object} options - 配置选项
   * @param {string} options.state - 状态参数
   * @param {string} options.redirectUri - 自定义回调地址
   * @param {string} options.scope - 授权范围
   * @returns {string} 授权URL
   */
  buildAuthUrlDirect(options = {}) {
    log(`buildAuthUrlDirect 开始，参数: ${JSON.stringify(options)}`)

    const {
      state = this.generateState(),
      redirectUri = this.getRedirectUri(),
      scope = this.config.scope || 'snsapi_userinfo'
    } = options

    const urlParams = {
      appid: this.config.appId,
      redirect_uri: redirectUri,
      response_type: 'code',
      scope: scope,
      state: state
    }

    log(`构造URL参数: ${JSON.stringify(urlParams)}`)

    // 检查必要参数
    if (!this.config.appId) {
      throw new Error('微信AppId未配置')
    }

    if (!redirectUri) {
      throw new Error('回调地址未配置')
    }

    const params = new URLSearchParams(urlParams)

    const authUrl = `https://open.weixin.qq.com/connect/oauth2/authorize?${params.toString()}#wechat_redirect`

    log(`直接构造的授权URL: ${authUrl}`)
    log(`URL长度: ${authUrl.length}`)

    return authUrl
  }

  /**
   * 解析微信授权回调参数
   * @param {string} url - 回调URL或查询字符串
   * @returns {Object}
   */
  parseCallbackParams(url = '') {
    let queryString = ''
    
    if (!url && typeof window !== 'undefined') {
      // 从当前页面URL获取参数
      queryString = window.location.search
    } else if (url.includes('?')) {
      // 从提供的URL中提取查询字符串
      queryString = url.split('?')[1]
    } else {
      // 直接作为查询字符串处理
      queryString = url
    }

    const params = new URLSearchParams(queryString)
    
    return {
      code: params.get('code'),
      state: params.get('state'),
      error: params.get('error'),
      error_description: params.get('error_description')
    }
  }

  /**
   * 启动微信授权流程
   * @param {Object} options - 配置选项
   * @param {Object} options.extraState - 额外的状态数据
   * @param {string} options.redirectUri - 自定义回调地址
   * @param {string} options.scope - 授权范围
   * @param {string} options.inviterId - 邀请人ID
   * @param {boolean} options.useDirect - 是否直接构造URL（跳过后端接口）
   * @returns {Promise<void>}
   */
  async startAuth(options = {}) {
    try {
      log(`startAuth 开始，参数: ${JSON.stringify(options)}`)

      // 检查环境
      const isH5 = this.isH5Environment()
      log(`检查H5环境: ${isH5}`)
      if (!isH5) {
        throw new Error('微信网页授权仅支持H5环境')
      }

      // 验证配置
      log('验证微信配置...')
      const validation = this.validateConfig()
      if (!validation.valid) {
        throw new Error(`微信配置错误: ${validation.errors.join(', ')}`)
      }
      log('微信配置验证通过')

      // 生成状态参数
      const state = this.generateState(options.extraState || {})
      log(`生成状态参数: ${state}`)

      const redirectUri = options.redirectUri || this.getRedirectUri()
      const scope = options.scope || this.config.scope || 'snsapi_userinfo'

      log(`授权参数: state=${state}, redirectUri=${redirectUri}, scope=${scope}`)

      let authUrl

      if (options.useDirect) {
        log('使用直接构造方式生成授权URL')
        // 直接构造授权URL
        authUrl = this.buildAuthUrlDirect({
          state,
          redirectUri,
          scope
        })
      } else {
        try {
          log('尝试通过后端接口获取授权URL')
          // 尝试通过后端接口获取授权URL
          authUrl = await this.buildAuthUrl({
            state,
            redirectUri,
            scope,
            inviterId: options.inviterId
          })
        } catch (error) {
          log(`后端接口获取授权URL失败，使用直接构造方式: ${error.message}`, 'warning')
          // 后端接口失败时，使用直接构造方式
          authUrl = this.buildAuthUrlDirect({
            state,
            redirectUri,
            scope
          })
        }
      }

      log(`最终授权URL: ${authUrl}`)
      log(`授权URL类型: ${typeof authUrl}`)

      // 确保authUrl是字符串
      if (typeof authUrl !== 'string') {
        throw new Error('授权URL格式错误: ' + typeof authUrl + ', 值: ' + JSON.stringify(authUrl))
      }

      // 验证URL格式
      if (!authUrl.startsWith('http')) {
        throw new Error('授权URL格式无效: ' + authUrl)
      }

      log('开始微信授权，准备跳转...')

      // 跳转到微信授权页面
      if (typeof window !== 'undefined') {
        log('执行页面跳转...')
        log(`跳转前当前URL: ${window.location.href}`)

        // 直接使用最简单的跳转方式
        log('执行页面跳转...')

        // 尝试多种跳转方式
        let jumpSuccess = false

        // 方式1: 直接赋值（最常用）
        try {
          window.location.href = authUrl
          log('方式1: location.href 已执行')
          jumpSuccess = true
        } catch (e) {
          log(`方式1失败: ${e.message}`, 'warning')
        }

        // 如果方式1失败，尝试方式2
        if (!jumpSuccess) {
          try {
            window.location.assign(authUrl)
            log('方式2: location.assign 已执行')
            jumpSuccess = true
          } catch (e) {
            log(`方式2失败: ${e.message}`, 'warning')
          }
        }

        // 如果还是失败，尝试方式3
        if (!jumpSuccess) {
          try {
            window.location.replace(authUrl)
            log('方式3: location.replace 已执行')
            jumpSuccess = true
          } catch (e) {
            log(`方式3失败: ${e.message}`, 'warning')
          }
        }

        // 最后尝试强制跳转
        if (!jumpSuccess) {
          try {
            window.open(authUrl, '_self')
            log('方式4: window.open 已执行')
          } catch (e) {
            log(`方式4失败: ${e.message}`, 'error')
            throw new Error('所有跳转方式都失败了')
          }
        }

        log('跳转命令已执行')

        // 等待一小段时间看是否跳转成功
        setTimeout(() => {
          log(`跳转后当前URL: ${window.location.href}`)
          if (window.location.href.includes('open.weixin.qq.com')) {
            log('跳转成功！', 'success')
          } else {
            log('跳转可能失败，URL未改变', 'warning')
          }
        }, 500)

      } else {
        throw new Error('window对象不存在，无法执行跳转')
      }
    } catch (error) {
      log(`启动微信授权失败: ${error.message}`, 'error')
      log(`错误堆栈: ${error.stack}`, 'error')
      throw error
    }
  }

  /**
   * 处理授权回调
   * @param {string} url - 回调URL
   * @returns {Object} 处理结果
   */
  handleCallback(url) {
    try {
      const params = this.parseCallbackParams(url)
      
      // 检查是否有错误
      if (params.error) {
        return {
          success: false,
          error: params.error,
          errorDescription: params.error_description,
          message: this.getErrorMessage(params.error)
        }
      }

      // 检查是否有授权码
      if (!params.code) {
        return {
          success: false,
          error: 'no_code',
          message: '未获取到授权码'
        }
      }

      // 解析状态参数
      const stateData = this.parseState(params.state)

      return {
        success: true,
        code: params.code,
        state: params.state,
        stateData: stateData
      }
    } catch (error) {
      console.error('处理授权回调失败:', error)
      return {
        success: false,
        error: 'callback_error',
        message: '处理授权回调失败: ' + error.message
      }
    }
  }

  /**
   * 获取错误信息
   * @param {string} errorCode - 错误码
   * @returns {string}
   */
  getErrorMessage(errorCode) {
    const errorMessages = {
      'access_denied': '用户拒绝授权',
      'invalid_request': '请求参数错误',
      'unauthorized_client': '客户端未授权',
      'unsupported_response_type': '不支持的响应类型',
      'invalid_scope': '无效的授权范围',
      'server_error': '服务器错误',
      'temporarily_unavailable': '服务暂时不可用'
    }

    return errorMessages[errorCode] || `授权失败: ${errorCode}`
  }

  /**
   * 验证配置是否完整
   * @returns {Object}
   */
  validateConfig() {
    const errors = []

    if (!this.config.appId) {
      errors.push('缺少微信AppId配置')
    }

    if (!this.config.authBaseUrl) {
      errors.push('缺少微信授权URL配置')
    }

    if (!this.config.redirectUri || 
        (!this.config.redirectUri.development && !this.config.redirectUri.production)) {
      errors.push('缺少回调地址配置')
    }

    return {
      valid: errors.length === 0,
      errors: errors
    }
  }
}

// 创建单例实例
const wechatWebAuth = new WechatWebAuth()

// 导出实例和类
export default wechatWebAuth
export { WechatWebAuth }

// 导出常用方法
export const isH5Environment = () => wechatWebAuth.isH5Environment()
export const isWechatBrowser = () => wechatWebAuth.isWechatBrowser()
export const startWechatAuth = (options) => wechatWebAuth.startAuth(options)
export const handleWechatCallback = (url) => wechatWebAuth.handleCallback(url)
