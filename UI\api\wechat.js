/**
 * 微信相关API接口
 * 处理微信OAuth2.0授权、回调等功能
 */

import request from '../utils/request.js'

/**
 * 获取微信OAuth2.0授权URL
 * @param {Object} params - 授权参数
 * @param {string} params.RedirectUri - 回调地址（必填）
 * @param {string} params.State - 状态参数（可选）
 * @param {string} params.Scope - 授权范围（可选，默认snsapi_userinfo）
 * @returns {Promise<ApiResult<string>>} 授权URL
 */
export function getWechatAuthorizeUrl(params) {
  return request.get('/Wechat/authorize', params)
}

/**
 * 微信OAuth2.0授权回调接口
 * @param {Object} params - 回调参数
 * @param {string} params.code - 微信授权码（必填）
 * @param {string} params.state - 状态参数（可选）
 * @returns {Promise<ApiResult<Object>>} 用户信息和token
 */
export function wechatAuthCallback(params) {
  return request.get('/Wechat/callback', params)
}

/**
 * 手动刷新微信基础access_token
 * @returns {Promise<ApiResult<Object>>} 刷新结果
 */
export function refreshWechatToken() {
  return request.post('/Wechat/token/refresh')
}

/**
 * 获取当前微信基础access_token状态
 * @returns {Promise<ApiResult<Object>>} token状态信息
 */
export function getWechatTokenStatus() {
  return request.get('/Wechat/token/status')
}

/**
 * 微信服务器验证接口（用于配置微信公众号）
 * @param {Object} params - 验证参数
 * @param {string} params.signature - 微信加密签名
 * @param {string} params.timestamp - 时间戳
 * @param {string} params.nonce - 随机数
 * @param {string} params.echostr - 随机字符串
 * @returns {Promise<ApiResult<string>>} 验证结果
 */
export function verifyWechatServer(params) {
  return request.get('/Wechat/verify', params)
}

/**
 * 微信登录数据传输对象
 * @typedef {Object} WechatLoginDto
 * @property {string} code - 微信授权码
 * @property {string} state - 状态参数
 * @property {string} employeeId - 员工ID（可选）
 * @property {number} batchId - 批次ID（可选）
 * @property {string} platform - 平台标识（h5/mp-weixin）
 */

/**
 * 微信登录响应数据
 * @typedef {Object} WechatLoginResponseDto
 * @property {string} token - 访问令牌
 * @property {Object} userInfo - 用户信息
 * @property {string} userInfo.id - 用户ID
 * @property {string} userInfo.nickname - 用户昵称
 * @property {string} userInfo.avatar - 用户头像
 * @property {string} userInfo.openid - 微信openid
 * @property {string} userInfo.unionid - 微信unionid（可选）
 */

/**
 * 微信授权URL参数
 * @typedef {Object} WechatAuthorizeParams
 * @property {string} RedirectUri - 回调地址
 * @property {string} State - 状态参数
 * @property {string} Scope - 授权范围
 */

/**
 * 微信回调参数
 * @typedef {Object} WechatCallbackParams
 * @property {string} Code - 授权码
 * @property {string} State - 状态参数
 */

/**
 * 验证微信授权参数
 * @param {WechatAuthorizeParams} params - 授权参数
 * @returns {Object} 验证结果
 */
export function validateWechatAuthorizeParams(params) {
  const errors = []

  if (!params.RedirectUri) {
    errors.push('回调地址不能为空')
  } else if (!isValidUrl(params.RedirectUri)) {
    errors.push('回调地址格式不正确')
  }

  if (params.Scope && !['snsapi_base', 'snsapi_userinfo'].includes(params.Scope)) {
    errors.push('授权范围只能是 snsapi_base 或 snsapi_userinfo')
  }

  return {
    valid: errors.length === 0,
    errors: errors
  }
}

/**
 * 验证微信回调参数
 * @param {WechatCallbackParams} params - 回调参数
 * @returns {Object} 验证结果
 */
export function validateWechatCallbackParams(params) {
  const errors = []

  if (!params.Code) {
    errors.push('授权码不能为空')
  }

  return {
    valid: errors.length === 0,
    errors: errors
  }
}

/**
 * 格式化微信用户数据
 * @param {Object} userData - 原始用户数据
 * @returns {Object} 格式化后的用户数据
 */
export function formatWechatUserData(userData) {
  if (!userData) return null

  return {
    id: userData.id || userData.userId || '',
    nickname: userData.nickname || userData.nickName || '微信用户',
    avatar: userData.avatar || userData.headimgurl || '',
    openid: userData.openid || '',
    unionid: userData.unionid || '',
    gender: userData.gender || 0,
    city: userData.city || '',
    province: userData.province || '',
    country: userData.country || '',
    language: userData.language || 'zh_CN'
  }
}

/**
 * 构建微信授权状态参数
 * @param {Object} extraData - 额外数据
 * @returns {string} 编码后的状态参数
 */
export function buildWechatState(extraData = {}) {
  const stateData = {
    timestamp: Date.now(),
    random: Math.random().toString(36).substr(2, 9),
    ...extraData
  }
  
  try {
    return btoa(JSON.stringify(stateData))
  } catch (error) {
    console.error('构建状态参数失败:', error)
    return ''
  }
}

/**
 * 解析微信授权状态参数
 * @param {string} state - 状态参数
 * @returns {Object|null} 解析后的状态数据
 */
export function parseWechatState(state) {
  try {
    if (!state) return null
    const decoded = atob(state)
    return JSON.parse(decoded)
  } catch (error) {
    console.error('解析状态参数失败:', error)
    return null
  }
}

/**
 * 检查URL是否有效
 * @param {string} url - URL字符串
 * @returns {boolean} 是否有效
 */
function isValidUrl(url) {
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

// 默认导出所有微信相关API
export default {
  getWechatAuthorizeUrl,
  wechatAuthCallback,
  refreshWechatToken,
  getWechatTokenStatus,
  verifyWechatServer,
  validateWechatAuthorizeParams,
  validateWechatCallbackParams,
  formatWechatUserData,
  buildWechatState,
  parseWechatState
}
