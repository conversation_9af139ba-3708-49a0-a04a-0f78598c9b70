<template>
  <view class="callback-container">
    <view class="callback-content">
      <!-- 加载状态 -->
      <view v-if="isLoading" class="loading-section">
        <u-loading-icon mode="spinner" size="40" color="#186BFF" />
        <text class="loading-text">{{ loadingText }}</text>
      </view>

      <!-- 错误状态 -->
      <view v-else-if="hasError" class="error-section">
        <u-icon name="close-circle" size="60" color="#F5222D" />
        <text class="error-title">授权失败</text>
        <text class="error-message">{{ errorMessage }}</text>
        <u-button type="primary" @click="handleRetry" class="retry-btn">
          重试
        </u-button>
        <u-button type="info" @click="goBack" class="back-btn">
          返回
        </u-button>
      </view>

      <!-- 成功状态 -->
      <view v-else-if="isSuccess" class="success-section">
        <u-icon name="checkmark-circle" size="60" color="#52C41A" />
        <text class="success-title">授权成功</text>
        <text class="success-message">正在跳转...</text>
      </view>
    </view>

    <!-- Toast -->
    <u-toast ref="uToast" />
  </view>
</template>

<script>
import wechatWebAuth from '@/utils/wechatWebAuth.js'
import { wechatWebLogin } from '@/utils/wechatUserService.js'

export default {
  data () {
    return {
      isLoading: true,
      hasError: false,
      isSuccess: false,
      errorMessage: '',
      loadingText: '正在处理授权信息...',
      callbackResult: null,
      retryCount: 0,
      maxRetries: 3
    }
  },

  onLoad (options) {
    console.log('微信授权回调页面加载，参数:', options)
    this.handleWechatCallback()
  },

  methods: {
    /**
     * 处理微信授权回调
     */
    async handleWechatCallback () {
      try {
        this.isLoading = true
        this.hasError = false
        this.loadingText = '正在处理授权信息...'

        // 解析回调参数
        const callbackResult = wechatWebAuth.handleCallback()
        this.callbackResult = callbackResult

        console.log('微信授权回调结果:', callbackResult)

        // 无论成功失败，都显示调试信息并跳转回去
        const debugInfo = {
          success: callbackResult.success,
          code: callbackResult.code,
          state: callbackResult.state,
          stateData: callbackResult.stateData,
          error: callbackResult.error,
          message: callbackResult.message,
          timestamp: new Date().toLocaleString()
        }

        console.log('微信授权回调调试信息:', debugInfo)

        if (!callbackResult.success) {
          // 授权失败，也要跳转回去显示调试信息
          setTimeout(() => {
            uni.reLaunch({
              url: `/pages/wechat-test/index?debug=${encodeURIComponent(JSON.stringify(debugInfo))}`
            })
          }, 2000)
          this.showError(callbackResult.message || '授权失败')
          return
        }

        // 检查是否有授权码
        if (!callbackResult.code) {
          debugInfo.error = 'no_code'
          debugInfo.message = '未获取到授权码'
          setTimeout(() => {
            uni.reLaunch({
              url: `/pages/wechat-test/index?debug=${encodeURIComponent(JSON.stringify(debugInfo))}`
            })
          }, 2000)
          this.showError('未获取到授权码')
          return
        }

        // 调用登录接口
        await this.performLogin(callbackResult)

      } catch (error) {
        console.error('处理微信授权回调失败:', error)
        this.showError('处理授权信息失败: ' + error.message)
      }
    },

    /**
     * 执行登录
     */
    async performLogin (callbackResult) {
      try {
        this.loadingText = '正在登录...'

        // 准备回调数据
        const callbackData = {
          code: callbackResult.code,
          state: callbackResult.state
        }

        console.log('准备调用登录接口，数据:', callbackData)

        // 调用H5端微信登录接口
        const response = await wechatWebLogin(callbackData)

        console.log('登录接口响应:', response)

        if (response.success && response.data) {
          // 登录成功，保存用户信息
          await this.handleLoginSuccess(response.data)
        } else {
          throw new Error(response.message || '登录失败')
        }

      } catch (error) {
        console.error('登录失败:', error)
        this.showError('登录失败: ' + error.message)
      }
    },

    /**
     * 处理登录成功
     */
    async handleLoginSuccess (loginData) {
      try {
        this.loadingText = '登录成功，正在跳转...'

        // 保存用户信息到本地存储
        if (loginData.userInfo) {
          uni.setStorageSync('wechatUserInfo', loginData.userInfo)
        }

        if (loginData.token) {
          uni.setStorageSync('wechatUserToken', loginData.token)
        }

        // 显示成功状态
        this.isLoading = false
        this.isSuccess = true

        this.showToastMessage('登录成功！', 'success')

        // 延迟跳转
        setTimeout(() => {
          this.redirectAfterLogin()
        }, 1500)

      } catch (error) {
        console.error('处理登录成功失败:', error)
        this.showError('保存登录信息失败: ' + error.message)
      }
    },

    /**
     * 登录后跳转
     */
    redirectAfterLogin () {
      try {
        // 直接跳转回测试页面，并携带调试信息
        const debugInfo = {
          code: this.callbackResult?.code,
          state: this.callbackResult?.state,
          stateData: this.callbackResult?.stateData,
          loginSuccess: true
        }

        console.log('登录成功，跳转回测试页面，调试信息:', debugInfo)

        // 跳转回测试页面
        uni.reLaunch({
          url: `/pages/wechat-test/index?debug=${encodeURIComponent(JSON.stringify(debugInfo))}`,
          fail: (error) => {
            console.error('跳转失败:', error)
            // 如果跳转失败，尝试普通跳转
            uni.navigateTo({
              url: '/pages/wechat-test/index'
            })
          }
        })

      } catch (error) {
        console.error('跳转失败:', error)
        this.showError('跳转失败: ' + error.message)
      }
    },

    /**
     * 显示错误
     */
    showError (message) {
      this.isLoading = false
      this.hasError = true
      this.errorMessage = message
      this.showToastMessage(message, 'error')
    },

    /**
     * 重试处理
     */
    async handleRetry () {
      if (this.retryCount >= this.maxRetries) {
        this.showToastMessage('重试次数过多，请稍后再试', 'warning')
        return
      }

      this.retryCount++
      console.log(`第 ${this.retryCount} 次重试`)

      await this.handleWechatCallback()
    },

    /**
     * 返回上一页
     */
    goBack () {
      // 尝试返回上一页
      const pages = getCurrentPages()
      if (pages.length > 1) {
        uni.navigateBack()
      } else {
        // 如果没有上一页，跳转到首页
        uni.reLaunch({
          url: '/pages/index/index'
        })
      }
    },

    /**
     * 显示Toast消息
     */
    showToastMessage (message, type = 'success') {
      this.$refs.uToast.show({
        message: message,
        type: type,
        duration: 3000
      })
    }
  }
}
</script>

<style scoped>
.callback-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f0f2f5 0%, #fafbfc 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx 30rpx;
  box-sizing: border-box;
}

.callback-content {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 80rpx 60rpx;
  box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.1);
  text-align: center;
  max-width: 600rpx;
  width: 100%;
}

/* 加载状态样式 */
.loading-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 30rpx;
}

.loading-text {
  font-size: 32rpx;
  color: #666666;
  margin-top: 20rpx;
}

/* 错误状态样式 */
.error-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 30rpx;
}

.error-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #F5222D;
  margin-top: 20rpx;
}

.error-message {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.5;
  text-align: center;
}

.retry-btn,
.back-btn {
  margin-top: 20rpx;
  width: 200rpx;
}

.back-btn {
  margin-top: 15rpx;
}

/* 成功状态样式 */
.success-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 30rpx;
}

.success-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #52C41A;
  margin-top: 20rpx;
}

.success-message {
  font-size: 28rpx;
  color: #666666;
}

/* 响应式适配 */
@media screen and (max-width: 750rpx) {
  .callback-content {
    padding: 60rpx 40rpx;
  }

  .error-title,
  .success-title {
    font-size: 32rpx;
  }

  .error-message,
  .success-message,
  .loading-text {
    font-size: 26rpx;
  }
}
</style>
