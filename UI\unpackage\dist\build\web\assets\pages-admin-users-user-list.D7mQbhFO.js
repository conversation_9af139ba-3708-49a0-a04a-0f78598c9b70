import{_ as e,v as s,S as t,I as a,r as n,b as r,x as i,c as o,w as c,a6 as l,a7 as u,i as d,a8 as h,a9 as m,aa as f,o as b,h as p,A as U,B as w,F as y}from"./index-Bl1z7-qC.js";import{U as g}from"./UserInfoCard.MVeS5fVN.js";import{q as C,f as T}from"./video-user.DmBugx73.js";import"./media-common.7odKdPQQ.js";import"./permission-mixin.QQrgJw9R.js";const F=e({components:{UserInfoCard:g},data:()=>({users:[],bannedUsers:[],loading:!1,currentTab:0,currentFilter:0,searchKeyword:"",tabList:[{name:"正常用户"},{name:"禁用用户"}],filterList:[{name:"全部"},{name:"今日新增"},{name:"活跃用户"},{name:"不活跃"}]}),computed:{filteredUsers(){let e=0===this.currentTab?this.users:this.bannedUsers;switch(this.searchKeyword.trim()&&(e=e.filter((e=>(e.username||e.nickname||"").toLowerCase().includes(this.searchKeyword.toLowerCase().trim())))),this.currentFilter){case 1:const s=(new Date).toISOString().split("T")[0];e=e.filter((e=>new Date(e.createTime||e.registerTime).toISOString().split("T")[0]===s));break;case 2:e=e.filter((e=>(e.watchedVideos||0)>0||(e.completedQuizzes||0)>0));break;case 3:e=e.filter((e=>0===(e.watchedVideos||0)&&0===(e.completedQuizzes||0)))}return e},normalUsersCount(){return this.users.length},bannedUsersCount(){return this.bannedUsers.length}},onLoad(){this.loadAllUsers()},methods:{async loadAllUsers(){this.loading=!0;try{const e=await C({PageIndex:1,PageSize:1e3});if(e.success&&e.data){const s=(e.data.items||e.data||[]).map((e=>T(e)));this.users=s.filter((e=>0!==e.status)),this.bannedUsers=s.filter((e=>0===e.status)),this.updateTabTitles()}}catch(e){console.error("加载用户数据失败:",e),s({title:"加载用户数据失败",icon:"none"})}finally{this.loading=!1}},updateTabTitles(){this.tabList=[{name:`正常用户(${this.normalUsersCount})`},{name:`禁用用户(${this.bannedUsersCount})`}]},formatUserInfo(e){return{...e,type:"user",disabled:1===this.currentTab,username:e.username||e.nickname,phone:e.phone||e.mobile||"未设置",avatar:e.avatar||"/assets/images/avatar-placeholder.png"}},switchTab(e,t){this.currentTab=t,this.currentFilter=0,s({title:0===t?"切换到正常用户":"切换到禁用用户",icon:"none",duration:1e3})},setFilter(e){this.currentFilter=e},onSearch(){},clearSearch(){this.searchKeyword=""},viewUserDetail(e){t({url:`/pages/admin/users/info?userId=${e.id}`})},async banUser(e){a({title:"确认禁用",content:`确定要禁用用户 ${e.username||e.nickname} 吗？`,success:async t=>{if(t.confirm)try{const t=this.users.findIndex((s=>s.id===e.id));if(t>-1){const e={...this.users.splice(t,1)[0],status:0};this.bannedUsers.push(e),this.updateTabTitles()}s({title:"禁用成功",icon:"success"})}catch(a){console.error("禁用用户失败:",a),s({title:"禁用失败",icon:"none"})}}})},async unbanUser(e){a({title:"确认启用",content:`确定要启用用户 ${e.username||e.nickname} 吗？`,success:async t=>{if(t.confirm)try{const t=this.bannedUsers.findIndex((s=>s.id===e.id));if(t>-1){const e={...this.bannedUsers.splice(t,1)[0],status:1};this.users.push(e),this.updateTabTitles()}s({title:"启用成功",icon:"success"})}catch(a){console.error("启用用户失败:",a),s({title:"启用失败",icon:"none"})}}})}}},[["render",function(e,s,t,a,g,C){const T=n(r("u-tabs"),l),F=n(r("u-search"),u),I=d,v=n(r("u-subsection"),h),k=n(r("u-loadmore"),m),S=i("UserInfoCard"),x=n(r("u-empty"),f);return b(),o(I,{class:"container"},{default:c((()=>[p(I,{class:"fixed-header"},{default:c((()=>[p(I,{class:"header-content"},{default:c((()=>[p(T,{list:g.tabList,current:g.currentTab,onChange:C.switchTab,lineColor:"#186BFF",activeColor:"#186BFF",inactiveColor:"#909399"},null,8,["list","current","onChange"]),p(I,{class:"search-container"},{default:c((()=>[p(F,{placeholder:"搜索用户名...",modelValue:g.searchKeyword,"onUpdate:modelValue":s[0]||(s[0]=e=>g.searchKeyword=e),onSearch:C.onSearch,onClear:C.clearSearch,showAction:!1},null,8,["modelValue","onSearch","onClear"])])),_:1}),p(v,{list:g.filterList,current:g.currentFilter,onChange:C.setFilter,activeColor:"#186BFF"},null,8,["list","current","onChange"])])),_:1})])),_:1}),p(I,{class:"list-container"},{default:c((()=>[g.loading?(b(),o(k,{key:0,status:"loading",loadingText:"加载中..."})):C.filteredUsers.length>0?(b(),o(I,{key:1,class:"user-list"},{default:c((()=>[(b(!0),U(y,null,w(C.filteredUsers,(e=>(b(),o(I,{class:"user-card-wrapper",key:e.id,onClick:s=>C.viewUserDetail(e)},{default:c((()=>[p(S,{userInfo:C.formatUserInfo(e),showDetailBtn:!1,showFooterBtns:!0,showUsersBtn:!1,showEmployeesBtn:!1,showAccountBtn:!0,onViewDetail:C.viewUserDetail,onDisableAccount:C.banUser,onEnableAccount:C.unbanUser},null,8,["userInfo","onViewDetail","onDisableAccount","onEnableAccount"])])),_:2},1032,["onClick"])))),128))])),_:1})):(b(),o(x,{key:2,mode:"data",text:"暂无用户数据"}))])),_:1})])),_:1})}],["__scopeId","data-v-0bbbf400"]]);export{F as default};
